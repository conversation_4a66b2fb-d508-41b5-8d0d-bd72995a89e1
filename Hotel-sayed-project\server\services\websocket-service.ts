import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { syncService, SyncResult } from './sync-service';

export interface WebSocketUser {
  userId: number;
  hotelId: number;
  socketId: string;
}

export class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, WebSocketUser> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:5000'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
    });

    this.setupEventHandlers();
    this.setupSyncServiceListeners();
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      console.log(`Client connected: ${socket.id}`);

      // Handle user authentication
      socket.on('authenticate', (data: { userId: number; hotelId: number }) => {
        this.connectedUsers.set(socket.id, {
          userId: data.userId,
          hotelId: data.hotelId,
          socketId: socket.id,
        });

        socket.join(`hotel-${data.hotelId}`);
        console.log(`User ${data.userId} authenticated for hotel ${data.hotelId}`);
        
        // Send current sync status
        this.sendSyncStatus(socket, data.hotelId);
      });

      // Handle manual sync trigger
      socket.on('triggerSync', async (data: { locationId: number; platform: string }) => {
        const user = this.connectedUsers.get(socket.id);
        if (!user) {
          socket.emit('error', { message: 'Not authenticated' });
          return;
        }

        try {
          const result = await syncService.triggerSync(data.locationId, data.platform);
          socket.emit('syncTriggered', result);
        } catch (error) {
          socket.emit('error', { 
            message: 'Failed to trigger sync',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log(`Client disconnected: ${socket.id}`);
        this.connectedUsers.delete(socket.id);
      });
    });
  }

  private setupSyncServiceListeners(): void {
    // Listen for sync start events
    syncService.on('syncStarted', (data: any) => {
      this.broadcastSyncStarted(data);
    });

    // Listen for sync completion events
    syncService.on('syncComplete', (result: SyncResult) => {
      this.broadcastSyncUpdate(result);

      if (result.newReviews > 0) {
        this.broadcastNewReviews(result);
      }
    });

    // Listen for sync error events
    syncService.on('syncError', (result: SyncResult) => {
      this.broadcastSyncError(result);
    });

    // Listen for new reviews found events
    syncService.on('newReviewsFound', (data: any) => {
      this.broadcastNewReviewsAlert(data);
    });

    // Listen for max retries exceeded events
    syncService.on('syncMaxRetriesExceeded', (data: any) => {
      this.broadcastSyncFailure(data);
    });
  }

  private async sendSyncStatus(socket: Socket, hotelId: number): Promise<void> {
    try {
      const syncStatuses = await syncService.getSyncStatuses(hotelId);
      socket.emit('syncStatus', syncStatuses);
    } catch (error) {
      console.error('Failed to send sync status:', error);
    }
  }

  private broadcastSyncUpdate(result: SyncResult): void {
    // Find the hotel ID for this location
    // Note: In a real implementation, you'd want to store location-to-hotel mapping
    this.io.emit('syncUpdate', {
      locationId: result.locationId,
      platform: result.platform,
      status: result.success ? 'completed' : 'failed',
      newReviews: result.newReviews,
      totalReviews: result.totalReviews,
      syncTime: result.syncTime,
      error: result.error,
    });
  }

  private broadcastNewReviews(result: SyncResult): void {
    // Broadcast notification about new reviews
    this.io.emit('newReviews', {
      locationId: result.locationId,
      platform: result.platform,
      count: result.newReviews,
      message: `${result.newReviews} new review${result.newReviews > 1 ? 's' : ''} received from ${result.platform}`,
    });
  }

  private broadcastSyncStarted(data: any): void {
    this.io.emit('syncStarted', {
      locationId: data.locationId,
      platform: data.platform,
      syncTime: data.syncTime,
      message: `Starting sync for ${data.platform} location ${data.locationId}`,
    });
  }

  private broadcastSyncError(result: SyncResult): void {
    this.io.emit('syncError', {
      locationId: result.locationId,
      platform: result.platform,
      error: result.error,
      syncTime: result.syncTime,
      duration: (result as any).duration,
    });
  }

  private broadcastNewReviewsAlert(data: any): void {
    this.io.emit('newReviewsAlert', {
      locationId: data.locationId,
      platform: data.platform,
      count: data.count,
      syncTime: data.syncTime,
      message: `🎉 ${data.count} new review${data.count > 1 ? 's' : ''} found on ${data.platform}!`,
      priority: 'high',
    });
  }

  private broadcastSyncFailure(data: any): void {
    this.io.emit('syncFailure', {
      locationId: data.locationId,
      platform: data.platform,
      error: data.error,
      retryCount: data.retryCount,
      message: `⚠️ Sync failed permanently for ${data.platform} location ${data.locationId} after ${data.retryCount} retries`,
      priority: 'critical',
    });
  }

  /**
   * Send a custom notification to users of a specific hotel
   */
  sendNotificationToHotel(hotelId: number, notification: any): void {
    this.io.to(`hotel-${hotelId}`).emit('notification', notification);
  }

  /**
   * Send a custom message to a specific user
   */
  sendMessageToUser(socketId: string, event: string, data: any): void {
    this.io.to(socketId).emit(event, data);
  }

  /**
   * Get connected users count
   */
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  /**
   * Get connected users for a specific hotel
   */
  getHotelUsers(hotelId: number): WebSocketUser[] {
    return Array.from(this.connectedUsers.values()).filter(user => user.hotelId === hotelId);
  }
}

let webSocketService: WebSocketService | null = null;

export function initializeWebSocket(server: HTTPServer): WebSocketService {
  if (!webSocketService) {
    webSocketService = new WebSocketService(server);
  }
  return webSocketService;
}

export function getWebSocketService(): WebSocketService | null {
  return webSocketService;
}
