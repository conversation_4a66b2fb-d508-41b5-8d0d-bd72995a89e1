{"version": 3, "sources": ["../src/index.ts", "../src/radio-group.tsx", "../src/radio.tsx"], "sourcesContent": ["'use client';\nexport {\n  createRadioGroupScope,\n  //\n  RadioGroup,\n  RadioGroupItem,\n  RadioGroupIndicator,\n  //\n  Root,\n  Item,\n  Indicator,\n} from './radio-group';\nexport type { RadioGroupProps, RadioGroupItemProps, RadioGroupIndicatorProps } from './radio-group';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Radio, RadioIndicator, createRadioScope } from './radio';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroup\n * -----------------------------------------------------------------------------------------------*/\nconst RADIO_GROUP_NAME = 'RadioGroup';\n\ntype ScopedProps<P> = P & { __scopeRadioGroup?: Scope };\nconst [createRadioGroupContext, createRadioGroupScope] = createContextScope(RADIO_GROUP_NAME, [\n  createRovingFocusGroupScope,\n  createRadioScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\nconst useRadioScope = createRadioScope();\n\ntype RadioGroupContextValue = {\n  name?: string;\n  required: boolean;\n  disabled: boolean;\n  value: string | null;\n  onValueChange(value: string): void;\n};\n\nconst [RadioGroupProvider, useRadioGroupContext] =\n  createRadioGroupContext<RadioGroupContextValue>(RADIO_GROUP_NAME);\n\ntype RadioGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RadioGroupProps extends PrimitiveDivProps {\n  name?: RadioGroupContextValue['name'];\n  required?: React.ComponentPropsWithoutRef<typeof Radio>['required'];\n  disabled?: React.ComponentPropsWithoutRef<typeof Radio>['disabled'];\n  dir?: RovingFocusGroupProps['dir'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  loop?: RovingFocusGroupProps['loop'];\n  defaultValue?: string;\n  value?: string | null;\n  onValueChange?: RadioGroupContextValue['onValueChange'];\n}\n\nconst RadioGroup = React.forwardRef<RadioGroupElement, RadioGroupProps>(\n  (props: ScopedProps<RadioGroupProps>, forwardedRef) => {\n    const {\n      __scopeRadioGroup,\n      name,\n      defaultValue,\n      value: valueProp,\n      required = false,\n      disabled = false,\n      orientation,\n      dir,\n      loop = true,\n      onValueChange,\n      ...groupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? null,\n      onChange: onValueChange as (value: string | null) => void,\n      caller: RADIO_GROUP_NAME,\n    });\n\n    return (\n      <RadioGroupProvider\n        scope={__scopeRadioGroup}\n        name={name}\n        required={required}\n        disabled={disabled}\n        value={value}\n        onValueChange={setValue}\n      >\n        <RovingFocusGroup.Root\n          asChild\n          {...rovingFocusGroupScope}\n          orientation={orientation}\n          dir={direction}\n          loop={loop}\n        >\n          <Primitive.div\n            role=\"radiogroup\"\n            aria-required={required}\n            aria-orientation={orientation}\n            data-disabled={disabled ? '' : undefined}\n            dir={direction}\n            {...groupProps}\n            ref={forwardedRef}\n          />\n        </RovingFocusGroup.Root>\n      </RadioGroupProvider>\n    );\n  }\n);\n\nRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RadioGroupItem';\n\ntype RadioGroupItemElement = React.ComponentRef<typeof Radio>;\ntype RadioProps = React.ComponentPropsWithoutRef<typeof Radio>;\ninterface RadioGroupItemProps extends Omit<RadioProps, 'onCheck' | 'name'> {\n  value: string;\n}\n\nconst RadioGroupItem = React.forwardRef<RadioGroupItemElement, RadioGroupItemProps>(\n  (props: ScopedProps<RadioGroupItemProps>, forwardedRef) => {\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = React.useRef<React.ComponentRef<typeof Radio>>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = React.useRef(false);\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (ARROW_KEYS.includes(event.key)) {\n          isArrowKeyPressedRef.current = true;\n        }\n      };\n      const handleKeyUp = () => (isArrowKeyPressedRef.current = false);\n      document.addEventListener('keydown', handleKeyDown);\n      document.addEventListener('keyup', handleKeyUp);\n      return () => {\n        document.removeEventListener('keydown', handleKeyDown);\n        document.removeEventListener('keyup', handleKeyUp);\n      };\n    }, []);\n\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!isDisabled}\n        active={checked}\n      >\n        <Radio\n          disabled={isDisabled}\n          required={context.required}\n          checked={checked}\n          {...radioScope}\n          {...itemProps}\n          name={context.name}\n          ref={composedRefs}\n          onCheck={() => context.onValueChange(itemProps.value)}\n          onKeyDown={composeEventHandlers((event) => {\n            // According to WAI ARIA, radio groups don't activate items on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onFocus={composeEventHandlers(itemProps.onFocus, () => {\n            /**\n             * Our `RovingFocusGroup` will focus the radio when navigating with arrow keys\n             * and we need to \"check\" it in that case. We click it to \"check\" it (instead\n             * of updating `context.value`) so that the radio change event fires.\n             */\n            if (isArrowKeyPressedRef.current) ref.current?.click();\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nRadioGroupItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioGroupIndicator';\n\ntype RadioGroupIndicatorElement = React.ComponentRef<typeof RadioIndicator>;\ntype RadioIndicatorProps = React.ComponentPropsWithoutRef<typeof RadioIndicator>;\ninterface RadioGroupIndicatorProps extends RadioIndicatorProps {}\n\nconst RadioGroupIndicator = React.forwardRef<RadioGroupIndicatorElement, RadioGroupIndicatorProps>(\n  (props: ScopedProps<RadioGroupIndicatorProps>, forwardedRef) => {\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return <RadioIndicator {...radioScope} {...indicatorProps} ref={forwardedRef} />;\n  }\n);\n\nRadioGroupIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = RadioGroup;\nconst Item = RadioGroupItem;\nconst Indicator = RadioGroupIndicator;\n\nexport {\n  createRadioGroupScope,\n  //\n  RadioGroup,\n  RadioGroupItem,\n  RadioGroupIndicator,\n  //\n  Root,\n  Item,\n  Indicator,\n};\nexport type { RadioGroupProps, RadioGroupItemProps, RadioGroupIndicatorProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Radio\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_NAME = 'Radio';\n\ntype ScopedProps<P> = P & { __scopeRadio?: Scope };\nconst [createRadioContext, createRadioScope] = createContextScope(RADIO_NAME);\n\ntype RadioContextValue = { checked: boolean; disabled?: boolean };\nconst [RadioProvider, useRadioContext] = createRadioContext<RadioContextValue>(RADIO_NAME);\n\ntype RadioElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface RadioProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  required?: boolean;\n  onCheck?(): void;\n}\n\nconst Radio = React.forwardRef<RadioElement, RadioProps>(\n  (props: ScopedProps<RadioProps>, forwardedRef) => {\n    const {\n      __scopeRadio,\n      name,\n      checked = false,\n      required,\n      disabled,\n      value = 'on',\n      onCheck,\n      form,\n      ...radioProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n\n    return (\n      <RadioProvider scope={__scopeRadio} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"radio\"\n          aria-checked={checked}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...radioProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            // radios cannot be unchecked so we only communicate a checked state\n            if (!checked) onCheck?.();\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if radio is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect radio updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <RadioBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </RadioProvider>\n    );\n  }\n);\n\nRadio.displayName = RADIO_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioIndicator';\n\ntype RadioIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\nexport interface RadioIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst RadioIndicator = React.forwardRef<RadioIndicatorElement, RadioIndicatorProps>(\n  (props: ScopedProps<RadioIndicatorProps>, forwardedRef) => {\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return (\n      <Presence present={forceMount || context.checked}>\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n        />\n      </Presence>\n    );\n  }\n);\n\nRadioIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'RadioBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface RadioBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst RadioBubbleInput = React.forwardRef<HTMLInputElement, RadioBubbleInputProps>(\n  (\n    {\n      __scopeRadio,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<RadioBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <Primitive.input\n        type=\"radio\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nRadioBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createRadioScope,\n  //\n  Radio,\n  RadioIndicator,\n};\nexport type { RadioProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,cAAAA;AAAA,EAAA;AAAA;AAAA;AAAA,cAAAC;AAAA,EAAA;AAAA;AAAA;;;ACAA,IAAAC,SAAuB;AACvB,IAAAC,oBAAqC;AACrC,IAAAC,6BAAgC;AAChC,IAAAC,wBAAmC;AACnC,IAAAC,0BAA0B;AAC1B,uBAAkC;AAClC,gCAA4C;AAC5C,0CAAqC;AACrC,6BAA6B;;;ACR7B,YAAuB;AACvB,uBAAqC;AACrC,gCAAgC;AAChC,2BAAmC;AACnC,4BAAwB;AACxB,gCAA4B;AAC5B,4BAAyB;AACzB,6BAA0B;AA4CpB;AApCN,IAAM,aAAa;AAGnB,IAAM,CAAC,oBAAoB,gBAAgB,QAAI,yCAAmB,UAAU;AAG5E,IAAM,CAAC,eAAe,eAAe,IAAI,mBAAsC,UAAU;AAUzF,IAAM,QAAc;AAAA,EAClB,CAAC,OAAgC,iBAAiB;AAChD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,CAAC,QAAQ,SAAS,IAAU,eAAmC,IAAI;AACzE,UAAM,mBAAe,2CAAgB,cAAc,CAAC,SAAS,UAAU,IAAI,CAAC;AAC5E,UAAM,mCAAyC,aAAO,KAAK;AAE3D,UAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,QAAQ,MAAM,IAAI;AAElE,WACE,6CAAC,iBAAc,OAAO,cAAc,SAAkB,UACpD;AAAA;AAAA,QAAC,iCAAU;AAAA,QAAV;AAAA,UACC,MAAK;AAAA,UACL,MAAK;AAAA,UACL,gBAAc;AAAA,UACd,cAAY,SAAS,OAAO;AAAA,UAC5B,iBAAe,WAAW,KAAK;AAAA,UAC/B;AAAA,UACA;AAAA,UACC,GAAG;AAAA,UACJ,KAAK;AAAA,UACL,aAAS,uCAAqB,MAAM,SAAS,CAAC,UAAU;AAEtD,gBAAI,CAAC,QAAS,WAAU;AACxB,gBAAI,eAAe;AACjB,+CAAiC,UAAU,MAAM,qBAAqB;AAItE,kBAAI,CAAC,iCAAiC,QAAS,OAAM,gBAAgB;AAAA,YACvE;AAAA,UACF,CAAC;AAAA;AAAA,MACH;AAAA,MACC,iBACC;AAAA,QAAC;AAAA;AAAA,UACC,SAAS;AAAA,UACT,SAAS,CAAC,iCAAiC;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UAIA,OAAO,EAAE,WAAW,oBAAoB;AAAA;AAAA,MAC1C;AAAA,OAEJ;AAAA,EAEJ;AACF;AAEA,MAAM,cAAc;AAMpB,IAAM,iBAAiB;AAYvB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,cAAc,YAAY,GAAG,eAAe,IAAI;AACxD,UAAM,UAAU,gBAAgB,gBAAgB,YAAY;AAC5D,WACE,4CAAC,kCAAS,SAAS,cAAc,QAAQ,SACvC;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACC,cAAY,SAAS,QAAQ,OAAO;AAAA,QACpC,iBAAe,QAAQ,WAAW,KAAK;AAAA,QACtC,GAAG;AAAA,QACJ,KAAK;AAAA;AAAA,IACP,GACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAM,oBAAoB;AAS1B,IAAM,mBAAyB;AAAA,EAC7B,CACE;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,GAAG;AAAA,EACL,GACA,iBACG;AACH,UAAM,MAAY,aAAyB,IAAI;AAC/C,UAAM,mBAAe,2CAAgB,KAAK,YAAY;AACtD,UAAM,kBAAc,uCAAY,OAAO;AACvC,UAAM,kBAAc,+BAAQ,OAAO;AAGnC,IAAM,gBAAU,MAAM;AACpB,YAAM,QAAQ,IAAI;AAClB,UAAI,CAAC,MAAO;AAEZ,YAAM,aAAa,OAAO,iBAAiB;AAC3C,YAAM,aAAa,OAAO;AAAA,QACxB;AAAA,QACA;AAAA,MACF;AACA,YAAM,aAAa,WAAW;AAC9B,UAAI,gBAAgB,WAAW,YAAY;AACzC,cAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC;AAC5C,mBAAW,KAAK,OAAO,OAAO;AAC9B,cAAM,cAAc,KAAK;AAAA,MAC3B;AAAA,IACF,GAAG,CAAC,aAAa,SAAS,OAAO,CAAC;AAElC,WACE;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACC,MAAK;AAAA,QACL,eAAW;AAAA,QACX,gBAAgB;AAAA,QACf,GAAG;AAAA,QACJ,UAAU;AAAA,QACV,KAAK;AAAA,QACL,OAAO;AAAA,UACL,GAAG,MAAM;AAAA,UACT,GAAG;AAAA,UACH,UAAU;AAAA,UACV,eAAe;AAAA,UACf,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,iBAAiB,cAAc;AAI/B,SAAS,SAAS,SAAkB;AAClC,SAAO,UAAU,YAAY;AAC/B;;;ADhHU,IAAAC,sBAAA;AAjFV,IAAM,aAAa,CAAC,WAAW,aAAa,aAAa,YAAY;AAKrE,IAAM,mBAAmB;AAGzB,IAAM,CAAC,yBAAyB,qBAAqB,QAAI,0CAAmB,kBAAkB;AAAA,EAC5F;AAAA,EACA;AACF,CAAC;AACD,IAAM,+BAA2B,uDAA4B;AAC7D,IAAM,gBAAgB,iBAAiB;AAUvC,IAAM,CAAC,oBAAoB,oBAAoB,IAC7C,wBAAgD,gBAAgB;AAiBlE,IAAM,aAAmB;AAAA,EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP,WAAW;AAAA,MACX,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA,OAAO;AAAA,MACP;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AACJ,UAAM,wBAAwB,yBAAyB,iBAAiB;AACxE,UAAM,gBAAY,qCAAa,GAAG;AAClC,UAAM,CAAC,OAAO,QAAQ,QAAI,0DAAqB;AAAA,MAC7C,MAAM;AAAA,MACN,aAAa,gBAAgB;AAAA,MAC7B,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAED,WACE;AAAA,MAAC;AAAA;AAAA,QACC,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe;AAAA,QAEf;AAAA,UAAkB;AAAA,UAAjB;AAAA,YACC,SAAO;AAAA,YACN,GAAG;AAAA,YACJ;AAAA,YACA,KAAK;AAAA,YACL;AAAA,YAEA;AAAA,cAAC,kCAAU;AAAA,cAAV;AAAA,gBACC,MAAK;AAAA,gBACL,iBAAe;AAAA,gBACf,oBAAkB;AAAA,gBAClB,iBAAe,WAAW,KAAK;AAAA,gBAC/B,KAAK;AAAA,gBACJ,GAAG;AAAA,gBACJ,KAAK;AAAA;AAAA,YACP;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,YAAY;AAQlB,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,mBAAmB,UAAU,GAAG,UAAU,IAAI;AACtD,UAAM,UAAU,qBAAqB,WAAW,iBAAiB;AACjE,UAAM,aAAa,QAAQ,YAAY;AACvC,UAAM,wBAAwB,yBAAyB,iBAAiB;AACxE,UAAM,aAAa,cAAc,iBAAiB;AAClD,UAAM,MAAY,cAAyC,IAAI;AAC/D,UAAM,mBAAe,4CAAgB,cAAc,GAAG;AACtD,UAAM,UAAU,QAAQ,UAAU,UAAU;AAC5C,UAAM,uBAA6B,cAAO,KAAK;AAE/C,IAAM,iBAAU,MAAM;AACpB,YAAM,gBAAgB,CAAC,UAAyB;AAC9C,YAAI,WAAW,SAAS,MAAM,GAAG,GAAG;AAClC,+BAAqB,UAAU;AAAA,QACjC;AAAA,MACF;AACA,YAAM,cAAc,MAAO,qBAAqB,UAAU;AAC1D,eAAS,iBAAiB,WAAW,aAAa;AAClD,eAAS,iBAAiB,SAAS,WAAW;AAC9C,aAAO,MAAM;AACX,iBAAS,oBAAoB,WAAW,aAAa;AACrD,iBAAS,oBAAoB,SAAS,WAAW;AAAA,MACnD;AAAA,IACF,GAAG,CAAC,CAAC;AAEL,WACE;AAAA,MAAkB;AAAA,MAAjB;AAAA,QACC,SAAO;AAAA,QACN,GAAG;AAAA,QACJ,WAAW,CAAC;AAAA,QACZ,QAAQ;AAAA,QAER;AAAA,UAAC;AAAA;AAAA,YACC,UAAU;AAAA,YACV,UAAU,QAAQ;AAAA,YAClB;AAAA,YACC,GAAG;AAAA,YACH,GAAG;AAAA,YACJ,MAAM,QAAQ;AAAA,YACd,KAAK;AAAA,YACL,SAAS,MAAM,QAAQ,cAAc,UAAU,KAAK;AAAA,YACpD,eAAW,wCAAqB,CAAC,UAAU;AAEzC,kBAAI,MAAM,QAAQ,QAAS,OAAM,eAAe;AAAA,YAClD,CAAC;AAAA,YACD,aAAS,wCAAqB,UAAU,SAAS,MAAM;AAMrD,kBAAI,qBAAqB,QAAS,KAAI,SAAS,MAAM;AAAA,YACvD,CAAC;AAAA;AAAA,QACH;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAMC,kBAAiB;AAMvB,IAAM,sBAA4B;AAAA,EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,mBAAmB,GAAG,eAAe,IAAI;AACjD,UAAM,aAAa,cAAc,iBAAiB;AAClD,WAAO,6CAAC,kBAAgB,GAAG,YAAa,GAAG,gBAAgB,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,oBAAoB,cAAcA;AAIlC,IAAMC,QAAO;AACb,IAAMC,QAAO;AACb,IAAM,YAAY;", "names": ["<PERSON><PERSON>", "Root", "React", "import_primitive", "import_react_compose_refs", "import_react_context", "import_react_primitive", "import_jsx_runtime", "INDICATOR_NAME", "Root", "<PERSON><PERSON>"]}