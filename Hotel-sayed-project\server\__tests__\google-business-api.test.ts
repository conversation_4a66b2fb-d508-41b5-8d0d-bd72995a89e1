// Set environment variables before any imports
process.env.GOOGLE_CLIENT_ID = 'test-client-id';
process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
process.env.GOOGLE_REDIRECT_URI = 'http://localhost:3000/api/auth/google/callback';

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { GoogleBusinessAPI } from '../services/google-business-api';
import { google } from 'googleapis';

// Mock the googleapis module
jest.mock('googleapis');
jest.mock('axios');
jest.mock('../storage');
jest.mock('../services/error-handler', () => ({
  errorHandler: {
    logError: jest.fn(),
    logGoogleBusinessError: jest.fn(),
    withRetry: jest.fn().mockImplementation(async (fn: any) => fn()),
  },
  ErrorCategory: {
    API_ERROR: 'api_error',
    AUTHENTICATION_ERROR: 'authentication_error',
    GOOGLE_API_ERROR: 'google_api_error',
    GOOGLE_AUTH_ERROR: 'google_auth_error',
    GOOGLE_QUOTA_ERROR: 'google_quota_error',
  },
  ErrorSeverity: {
    HIGH: 'high',
    MEDIUM: 'medium',
  },
}));
jest.mock('../storage', () => ({
  storage: {
    getUser: jest.fn(),
    getUserByUsername: jest.fn(),
    createUser: jest.fn(),
    getHotel: jest.fn(),
    createHotel: jest.fn(),
    listHotels: jest.fn(),
    getPlatformToken: jest.fn(),
    savePlatformToken: jest.fn(),
    getReview: jest.fn(),
    getReviewByExternalId: jest.fn(),
    listReviews: jest.fn(),
    saveReview: jest.fn(),
    saveReply: jest.fn(),
  }
}));
jest.mock('@shared/schema', () => ({
  users: {},
  hotels: {},
  platformTokens: {},
  reviews: {},
  replies: {},
  googleBusinessLocations: {},
  tripAdvisorLocations: {},
  bookingProperties: {},
  airbnbListings: {},
  syncStatus: {},
}));

const mockGoogle = google as jest.Mocked<typeof google>;

describe('GoogleBusinessAPI', () => {
  let googleAPI: GoogleBusinessAPI;
  let mockOAuth2Client: any;
  let mockBusinessProfileAPI: any;
  let mockPlacesAPI: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock OAuth2 client
    mockOAuth2Client = {
      generateAuthUrl: jest.fn(),
      getToken: jest.fn(),
      setCredentials: jest.fn(),
      refreshAccessToken: jest.fn(),
      credentials: {}
    };

    // Mock Business Profile API
    mockBusinessProfileAPI = {
      accounts: {
        locations: {
          list: jest.fn()
        }
      }
    };

    // Mock Places API
    mockPlacesAPI = {
      places: {
        get: jest.fn()
      }
    };

    // Mock google.auth.OAuth2 constructor
    mockGoogle.auth = {
      OAuth2: jest.fn().mockImplementation(() => mockOAuth2Client)
    } as any;

    // Mock API clients
    (mockGoogle as any).mybusinessbusinessinformation = jest.fn().mockReturnValue(mockBusinessProfileAPI);
    (mockGoogle as any).places = jest.fn().mockReturnValue(mockPlacesAPI);

    // Create a properly typed mock for account management API
    const mockAccountsAPI = {
      accounts: {
        list: jest.fn()
      }
    };
    (mockGoogle as any).mybusinessaccountmanagement = jest.fn().mockReturnValue(mockAccountsAPI);

    googleAPI = new GoogleBusinessAPI();
  });

  describe('Constructor', () => {
    it('should initialize OAuth2 client with correct credentials', () => {
      expect(mockGoogle.auth.OAuth2).toHaveBeenCalledWith(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.GOOGLE_REDIRECT_URI
      );
    });

    it('should initialize Business Profile API client', () => {
      expect(mockGoogle.mybusinessbusinessinformation).toHaveBeenCalledWith({
        version: 'v1',
        auth: mockOAuth2Client
      });
    });

    it('should initialize Places API client', () => {
      expect(mockGoogle.places).toHaveBeenCalledWith({
        version: 'v1',
        auth: mockOAuth2Client
      });
    });
  });

  describe('getAuthUrl', () => {
    it('should generate auth URL with correct scopes', () => {
      const mockAuthUrl = 'https://accounts.google.com/oauth2/auth?...';
      mockOAuth2Client.generateAuthUrl.mockReturnValue(mockAuthUrl);

      const result = googleAPI.getAuthUrl();

      expect(mockOAuth2Client.generateAuthUrl).toHaveBeenCalledWith({
        access_type: 'offline',
        scope: [
          'https://www.googleapis.com/auth/business.manage',
          'https://www.googleapis.com/auth/places'
        ],
        prompt: 'consent',
        include_granted_scopes: true
      });
      expect(result).toBe(mockAuthUrl);
    });
  });

  describe('getTokensFromCode', () => {
    it('should exchange code for tokens', async () => {
      const mockTokens = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expiry_date: Date.now() + 3600000
      };

      mockOAuth2Client.getToken.mockResolvedValue({ tokens: mockTokens });

      const result = await googleAPI.getTokensFromCode('test-code');

      expect(mockOAuth2Client.getToken).toHaveBeenCalledWith('test-code');
      expect(result).toEqual({
        access_token: mockTokens.access_token,
        refresh_token: mockTokens.refresh_token,
        expiry_date: mockTokens.expiry_date
      });
    });
  });

  describe('setCredentials', () => {
    it('should set credentials on OAuth2 client', () => {
      const credentials = {
        access_token: 'test-token',
        refresh_token: 'test-refresh',
        expiry_date: Date.now() + 3600000
      };

      googleAPI.setCredentials(credentials);

      expect(mockOAuth2Client.setCredentials).toHaveBeenCalledWith(credentials);
    });
  });

  describe('ensureValidCredentials', () => {
    it('should return true for valid non-expired credentials', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      const result = await googleAPI.ensureValidCredentials();

      expect(result).toBe(true);
    });

    it('should return false when no access token', async () => {
      mockOAuth2Client.credentials = {};

      const result = await googleAPI.ensureValidCredentials();

      expect(result).toBe(false);
    });

    it('should refresh expired token when refresh token available', async () => {
      const expiredTime = Date.now() - 1000;
      mockOAuth2Client.credentials = {
        access_token: 'expired-token',
        refresh_token: 'refresh-token',
        expiry_date: expiredTime
      };

      const newCredentials = {
        access_token: 'new-token',
        expiry_date: Date.now() + 3600000
      };

      mockOAuth2Client.refreshAccessToken.mockResolvedValue({
        credentials: newCredentials
      });

      const result = await googleAPI.ensureValidCredentials();

      expect(mockOAuth2Client.refreshAccessToken).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('getReviews', () => {
    it('should return fallback reviews when place ID cannot be extracted', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      const result = await googleAPI.getReviews('invalid-location-name');

      expect(result.reviews).toHaveLength(2);
      expect(result.reviews[0].reviewer.displayName).toBe('Sample Customer');
      expect(result.totalReviewCount).toBe(2);
    });

    it('should handle API errors gracefully', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      // Mock axios to throw an error
      const axios = require('axios');
      jest.mocked(axios.get).mockRejectedValue(new Error('API Error'));

      const result = await googleAPI.getReviews('locations/test-place-id');

      expect(result.reviews).toHaveLength(2); // Should return fallback reviews
      expect(result.reviews[0].reviewer.displayName).toBe('Sample Customer');
    });
  });

  describe('replyToReview', () => {
    it('should successfully post review reply', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      const axios = require('axios');
      jest.mocked(axios.post).mockResolvedValue({
        data: { success: true }
      });

      const result = await googleAPI.replyToReview('locations/test/reviews/123', 'Thank you for your review!');

      expect(result.success).toBe(true);
      expect(result.message).toBe('Review reply posted successfully');
      expect(axios.post).toHaveBeenCalledWith(
        'https://mybusiness.googleapis.com/v4/locations/test/reviews/123/reply',
        { comment: 'Thank you for your review!' },
        {
          headers: {
            'Authorization': 'Bearer valid-token',
            'Content-Type': 'application/json',
          }
        }
      );
    });

    it('should handle API access denied gracefully', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      const axios = require('axios');
      jest.mocked(axios.post).mockRejectedValue({
        response: { status: 403 },
        message: 'Access denied'
      });

      const result = await googleAPI.replyToReview('locations/test/reviews/123', 'Thank you!');

      expect(result.success).toBe(false);
      expect(result.requiresManualProcessing).toBe(true);
      expect(result.message).toContain('manual processing');
    });

    it('should handle invalid credentials', async () => {
      mockOAuth2Client.credentials = {};

      const result = await googleAPI.replyToReview('locations/test/reviews/123', 'Thank you!');

      expect(result.success).toBe(false);
      expect(result.message).toContain('credentials');
    });
  });

  describe('Integration with retry mechanism', () => {
    it('should use retry mechanism for getAccounts', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      // Mock the account management API directly on the google object
      const mockListFn = jest.fn() as jest.MockedFunction<any>;
      (mockListFn as any).mockResolvedValue({
        data: { accounts: [{ name: 'accounts/123', displayName: 'Test Account' }] }
      });

      (mockGoogle as any).mybusinessaccountmanagement = jest.fn().mockReturnValue({
        accounts: {
          list: mockListFn
        }
      });

      const accounts = await googleAPI.getAccounts();

      expect(accounts).toHaveLength(1);
      expect(accounts[0].name).toBe('accounts/123');
    });
  });

  describe('Error handling', () => {
    it('should handle network errors gracefully', async () => {
      mockOAuth2Client.credentials = {
        access_token: 'valid-token',
        expiry_date: Date.now() + 3600000
      };

      // Mock the account management API to throw an error
      const mockListFn = jest.fn() as jest.MockedFunction<any>;
      (mockListFn as any).mockRejectedValue(new Error('Network timeout'));

      (mockGoogle as any).mybusinessaccountmanagement = jest.fn().mockReturnValue({
        accounts: {
          list: mockListFn
        }
      });

      await expect(googleAPI.getAccounts()).rejects.toThrow('failed after');
    });
  });
});
