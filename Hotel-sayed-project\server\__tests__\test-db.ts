import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from "@shared/schema";
import path from 'path';

// Create a test-specific database
const TEST_DB_PATH = path.join(__dirname, '../../test-database.sqlite');

let testDb: any = null;

export function getTestDb() {
  if (!testDb) {
    try {
      const sqlite = new Database(TEST_DB_PATH);
      testDb = drizzle(sqlite, { schema });
      
      // Create tables if they don't exist
      sqlite.exec(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL,
          full_name TEXT NOT NULL,
          role TEXT DEFAULT 'staff',
          hotel_id INTEGER
        );
        
        CREATE TABLE IF NOT EXISTS hotels (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          address TEXT NOT NULL
        );
        
        CREATE TABLE IF NOT EXISTS platform_tokens (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          hotel_id INTEGER NOT NULL,
          platform TEXT NOT NULL,
          access_token TEXT NOT NULL,
          refresh_token TEXT,
          expires_at INTEGER,
          google_account_id TEXT,
          google_account_name TEXT
        );
        
        CREATE TABLE IF NOT EXISTS reviews (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          hotel_id INTEGER NOT NULL,
          platform TEXT NOT NULL,
          external_id TEXT NOT NULL,
          author_name TEXT NOT NULL,
          author_image TEXT,
          rating INTEGER NOT NULL,
          content TEXT NOT NULL,
          date INTEGER NOT NULL,
          is_replied INTEGER DEFAULT 0
        );
        
        CREATE TABLE IF NOT EXISTS replies (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          review_id INTEGER NOT NULL,
          content TEXT NOT NULL,
          user_id INTEGER NOT NULL,
          date INTEGER NOT NULL,
          is_posted INTEGER DEFAULT 0
        );
      `);
    } catch (error) {
      console.warn('Failed to create test database, using mock:', error);
      // Return a mock database if SQLite fails
      testDb = createMockDb();
    }
  }
  return testDb;
}

function createMockDb() {
  return {
    select: () => ({
      from: () => ({
        where: () => ({
          limit: () => Promise.resolve([]),
        }),
      }),
    }),
    insert: () => ({
      values: () => ({
        returning: () => Promise.resolve([]),
      }),
    }),
    update: () => ({
      set: () => ({
        where: () => ({
          returning: () => Promise.resolve([]),
        }),
      }),
    }),
    delete: () => ({
      where: () => Promise.resolve([]),
    }),
  };
}

export function closeTestDb() {
  if (testDb && testDb.close) {
    testDb.close();
  }
  testDb = null;
}
