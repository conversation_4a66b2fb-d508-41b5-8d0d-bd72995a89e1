import { storage } from '../storage';
import { googleBusinessAPI } from './google-business-api';
import { tripAdvisorAPI } from './tripadvisor-api';
import { bookingAPI } from './booking-api';
import { airbnbAPI } from './airbnb-api';
import { syncService } from './sync-service';

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  uptime: number;
  version: string;
  services: {
    database: ServiceHealth;
    googleAPI: ServiceHealth;
    tripAdvisorAPI: ServiceHealth;
    bookingAPI: ServiceHealth;
    airbnbAPI: ServiceHealth;
    syncService: ServiceHealth;
  };
  metrics: {
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: number;
    activeConnections: number;
    totalRequests: number;
    errorRate: number;
  };
}

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  lastCheck: number;
  error?: string;
  details?: any;
}

class HealthCheckService {
  private startTime: number;
  private totalRequests = 0;
  private totalErrors = 0;
  private activeConnections = 0;

  constructor() {
    this.startTime = Date.now();
  }

  /**
   * Increment request counter
   */
  incrementRequests(): void {
    this.totalRequests++;
  }

  /**
   * Increment error counter
   */
  incrementErrors(): void {
    this.totalErrors++;
  }

  /**
   * Set active connections count
   */
  setActiveConnections(count: number): void {
    this.activeConnections = count;
  }

  /**
   * Get comprehensive health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    const now = Date.now();
    const uptime = now - this.startTime;

    // Check all services
    const [
      databaseHealth,
      googleAPIHealth,
      tripAdvisorAPIHealth,
      bookingAPIHealth,
      airbnbAPIHealth,
      syncServiceHealth
    ] = await Promise.all([
      this.checkDatabase(),
      this.checkGoogleAPI(),
      this.checkTripAdvisorAPI(),
      this.checkBookingAPI(),
      this.checkAirbnbAPI(),
      this.checkSyncService()
    ]);

    // Determine overall status
    const services = {
      database: databaseHealth,
      googleAPI: googleAPIHealth,
      tripAdvisorAPI: tripAdvisorAPIHealth,
      bookingAPI: bookingAPIHealth,
      airbnbAPI: airbnbAPIHealth,
      syncService: syncServiceHealth
    };

    const overallStatus = this.calculateOverallStatus(services);

    return {
      status: overallStatus,
      timestamp: now,
      uptime,
      version: process.env.npm_package_version || '1.0.0',
      services,
      metrics: {
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage().user / 1000000, // Convert to seconds
        activeConnections: this.activeConnections,
        totalRequests: this.totalRequests,
        errorRate: this.totalRequests > 0 ? (this.totalErrors / this.totalRequests) * 100 : 0
      }
    };
  }

  /**
   * Check database health
   */
  private async checkDatabase(): Promise<ServiceHealth> {
    const startTime = Date.now();
    try {
      // Simple database query to check connectivity
      await storage.listHotels();
      
      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown database error'
      };
    }
  }

  /**
   * Check Google API health
   */
  private async checkGoogleAPI(): Promise<ServiceHealth> {
    const startTime = Date.now();
    try {
      // Simple check - just verify the API is initialized
      const hasCredentials = process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET;
      
      return {
        status: hasCredentials ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        details: { hasCredentials }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown Google API error'
      };
    }
  }

  /**
   * Check TripAdvisor API health
   */
  private async checkTripAdvisorAPI(): Promise<ServiceHealth> {
    const startTime = Date.now();
    try {
      const hasCredentials = process.env.TRIPADVISOR_API_KEY;
      
      return {
        status: hasCredentials ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        details: { hasCredentials: !!hasCredentials }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown TripAdvisor API error'
      };
    }
  }

  /**
   * Check Booking API health
   */
  private async checkBookingAPI(): Promise<ServiceHealth> {
    const startTime = Date.now();
    try {
      const hasCredentials = process.env.BOOKING_API_KEY && process.env.BOOKING_API_SECRET;
      
      return {
        status: hasCredentials ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        details: { hasCredentials: !!hasCredentials }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown Booking API error'
      };
    }
  }

  /**
   * Check Airbnb API health
   */
  private async checkAirbnbAPI(): Promise<ServiceHealth> {
    const startTime = Date.now();
    try {
      const hasCredentials = process.env.AIRBNB_API_KEY;
      
      return {
        status: hasCredentials ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        details: { hasCredentials: !!hasCredentials }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown Airbnb API error'
      };
    }
  }

  /**
   * Check sync service health
   */
  private async checkSyncService(): Promise<ServiceHealth> {
    const startTime = Date.now();
    try {
      // Check if sync service is running
      const isRunning = (syncService as any).isRunning;
      
      return {
        status: isRunning ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        details: { isRunning }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: Date.now(),
        error: error instanceof Error ? error.message : 'Unknown sync service error'
      };
    }
  }

  /**
   * Calculate overall status based on service statuses
   */
  private calculateOverallStatus(services: Record<string, ServiceHealth>): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(services).map(service => service.status);
    
    if (statuses.includes('unhealthy')) {
      return 'unhealthy';
    }
    
    if (statuses.includes('degraded')) {
      return 'degraded';
    }
    
    return 'healthy';
  }

  /**
   * Get simple readiness check
   */
  async getReadinessStatus(): Promise<{ ready: boolean; services: string[] }> {
    const health = await this.getHealthStatus();
    const unhealthyServices = Object.entries(health.services)
      .filter(([_, service]) => service.status === 'unhealthy')
      .map(([name, _]) => name);

    return {
      ready: health.status !== 'unhealthy',
      services: unhealthyServices
    };
  }

  /**
   * Get simple liveness check
   */
  getLivenessStatus(): { alive: boolean; uptime: number } {
    return {
      alive: true,
      uptime: Date.now() - this.startTime
    };
  }
}

export const healthCheckService = new HealthCheckService();
