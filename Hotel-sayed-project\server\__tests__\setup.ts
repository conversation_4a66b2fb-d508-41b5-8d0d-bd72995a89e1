import { beforeAll, afterAll, beforeEach, describe, it, expect } from '@jest/globals';
import fs from 'fs';
import path from 'path';

const TEST_DB_PATH = path.join(__dirname, '../../test-database.sqlite');

beforeAll(async () => {
  // Set up test environment
  process.env.NODE_ENV = 'test';
  process.env.SESSION_SECRET = 'test-secret';
  process.env.GOOGLE_CLIENT_ID = 'test-client-id';
  process.env.GOOGLE_CLIENT_SECRET = 'test-client-secret';
  process.env.GOOGLE_REDIRECT_URI = 'http://localhost:3000/api/auth/google/callback';

  // Clean up any existing test database
  if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
  }
});

beforeEach(async () => {
  // Clean up database before each test
  if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
  }
});

afterAll(async () => {
  // Clean up test database
  if (fs.existsSync(TEST_DB_PATH)) {
    fs.unlinkSync(TEST_DB_PATH);
  }
});

// Add a basic test to satisfy Jest's requirement
describe('Test Setup', () => {
  it('should set up test environment correctly', () => {
    expect(process.env.NODE_ENV).toBe('test');
    expect(process.env.SESSION_SECRET).toBe('test-secret');
    expect(process.env.GOOGLE_CLIENT_ID).toBe('test-client-id');
  });
});
