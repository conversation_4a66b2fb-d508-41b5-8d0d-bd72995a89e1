import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth } from "./auth";
import { insertReplySchema, reviewFilterSchema } from "@shared/schema";
import { googleBusinessAPI, GoogleBusinessAPI } from "./services/google-business-api";
import { syncService } from "./services/sync-service";
import { initializeWebSocket, getWebSocketService } from "./services/websocket-service";
import { healthCheckService } from "./services/health-check";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Health check routes (no auth required)
  app.get("/health", async (req, res) => {
    try {
      const health = await healthCheckService.getHealthStatus();
      const statusCode = health.status === 'healthy' ? 200 :
                        health.status === 'degraded' ? 200 : 503;
      res.status(statusCode).json(health);
    } catch (error) {
      res.status(503).json({
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      });
    }
  });

  app.get("/health/ready", async (req, res) => {
    try {
      const readiness = await healthCheckService.getReadinessStatus();
      const statusCode = readiness.ready ? 200 : 503;
      res.status(statusCode).json(readiness);
    } catch (error) {
      res.status(503).json({
        ready: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.get("/health/live", (req, res) => {
    const liveness = healthCheckService.getLivenessStatus();
    res.json(liveness);
  });

  // Metrics endpoint (basic)
  app.get("/metrics", async (req, res) => {
    try {
      const health = await healthCheckService.getHealthStatus();
      res.set('Content-Type', 'text/plain');
      res.send(`
# HELP hotel_app_uptime_seconds Application uptime in seconds
# TYPE hotel_app_uptime_seconds counter
hotel_app_uptime_seconds ${health.uptime / 1000}

# HELP hotel_app_memory_usage_bytes Memory usage in bytes
# TYPE hotel_app_memory_usage_bytes gauge
hotel_app_memory_usage_bytes{type="rss"} ${health.metrics.memoryUsage.rss}
hotel_app_memory_usage_bytes{type="heapUsed"} ${health.metrics.memoryUsage.heapUsed}
hotel_app_memory_usage_bytes{type="heapTotal"} ${health.metrics.memoryUsage.heapTotal}

# HELP hotel_app_requests_total Total number of requests
# TYPE hotel_app_requests_total counter
hotel_app_requests_total ${health.metrics.totalRequests}

# HELP hotel_app_error_rate_percent Error rate percentage
# TYPE hotel_app_error_rate_percent gauge
hotel_app_error_rate_percent ${health.metrics.errorRate}

# HELP hotel_app_active_connections Current active connections
# TYPE hotel_app_active_connections gauge
hotel_app_active_connections ${health.metrics.activeConnections}
      `.trim());
    } catch (error) {
      res.status(500).send('# Error generating metrics');
    }
  });

  // Set up authentication routes
  setupAuth(app);

  // Middleware to track requests
  app.use((req, res, next) => {
    healthCheckService.incrementRequests();

    // Track errors
    const originalSend = res.send;
    res.send = function(data) {
      if (res.statusCode >= 400) {
        healthCheckService.incrementErrors();
      }
      return originalSend.call(this, data);
    };

    next();
  });

  // Simple rate limiting implementation
  const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
  const rateLimit = (maxRequests: number, windowMs: number) => {
    return (req: Request, res: Response, next: Function) => {
      const clientId = req.ip || 'unknown';
      const now = Date.now();
      const windowStart = Math.floor(now / windowMs) * windowMs;
      const key = `${clientId}-${windowStart}`;

      const current = rateLimitMap.get(key) || { count: 0, resetTime: windowStart + windowMs };

      if (current.count >= maxRequests) {
        return res.status(429).json({
          error: 'Too many requests',
          retryAfter: Math.ceil((current.resetTime - now) / 1000)
        });
      }

      current.count++;
      rateLimitMap.set(key, current);

      // Clean up old entries
      if (Math.random() < 0.01) { // 1% chance to clean up
        Array.from(rateLimitMap.entries()).forEach(([k, v]) => {
          if (v.resetTime < now) {
            rateLimitMap.delete(k);
          }
        });
      }

      next();
    };
  };

  // User must be authenticated for all API routes except auth and health routes
  const requireAuth = (req: Request, res: Response, next: Function) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Unauthorized" });
    }
    next();
  };

  // Apply rate limiting to API routes
  const apiRateLimit = rateLimit(100, 60000); // 100 requests per minute
  const strictRateLimit = rateLimit(20, 60000); // 20 requests per minute for sensitive operations
  const authRateLimit = rateLimit(10, 60000); // 10 requests per minute for auth operations

  // Hotel routes
  app.get("/api/hotels", apiRateLimit, requireAuth, async (req, res) => {
    try {
      const hotels = await storage.listHotels();
      res.json(hotels);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch hotels" });
    }
  });

  // Review routes
  app.get("/api/reviews", apiRateLimit, requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.query.hotelId) || 1; // Default to hotel 1 for MVP
      
      // Parse and validate filter parameters
      const filterParams = {
        platform: req.query.platform as string,
        replyStatus: req.query.replyStatus as string,
        sortBy: req.query.sortBy as string,
        search: req.query.search as string,
        page: req.query.page ? Number(req.query.page) : 1,
        limit: req.query.limit ? Number(req.query.limit) : 10
      };
      
      const validatedFilters = reviewFilterSchema.parse(filterParams);
      
      // Convert reply status to boolean for filtering
      let isReplied: boolean | undefined = undefined;
      if (validatedFilters.replyStatus === 'replied') {
        isReplied = true;
      } else if (validatedFilters.replyStatus === 'not-replied') {
        isReplied = false;
      }
      
      const { reviews, total } = await storage.listReviews(hotelId, {
        platform: validatedFilters.platform !== 'all' ? validatedFilters.platform : undefined,
        isReplied,
        search: validatedFilters.search,
        sortBy: validatedFilters.sortBy,
        page: validatedFilters.page,
        limit: validatedFilters.limit
      });
      
      res.json({
        reviews,
        total,
        page: validatedFilters.page,
        limit: validatedFilters.limit,
        totalPages: Math.ceil(total / (validatedFilters.limit || 10))
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid filter parameters", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to fetch reviews" });
    }
  });

  app.get("/api/reviews/:id", requireAuth, async (req, res) => {
    try {
      const id = Number(req.params.id);
      const review = await storage.getReview(id);
      
      if (!review) {
        return res.status(404).json({ message: "Review not found" });
      }
      
      res.json(review);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch review" });
    }
  });

  // Reply routes
  app.post("/api/reviews/:id/reply", strictRateLimit, requireAuth, async (req, res) => {
    try {
      const reviewId = Number(req.params.id);
      const review = await storage.getReview(reviewId);
      
      if (!review) {
        return res.status(404).json({ message: "Review not found" });
      }

      const replyData = insertReplySchema.parse({
        reviewId,
        content: req.body.content,
        userId: req.user!.id
      });
      
      const reply = await storage.saveReply(replyData);
      
      // In a real app, this would make an API call to post the reply to the platform
      // For MVP, we mark it as posted locally
      
      res.json(reply);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid reply data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to save reply" });
    }
  });

  // Platform connection routes
  app.get("/api/platforms/connected", requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.query.hotelId) || 1; // Default to hotel 1 for MVP

      // Check which platforms are connected for this hotel
      const platforms = ["google", "booking", "airbnb", "tripadvisor"];
      const connectedPlatforms = await Promise.all(
        platforms.map(async (platform) => {
          const token = await storage.getPlatformToken(hotelId, platform);
          return {
            platform,
            connected: !!token
          };
        })
      );

      res.json(connectedPlatforms);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch connected platforms" });
    }
  });

  // Google OAuth routes
  app.get("/api/auth/google", authRateLimit, async (req, res) => {
    try {
      const authUrl = googleBusinessAPI.getAuthUrl();
      res.json({ authUrl });
    } catch (error) {
      console.error("Error generating Google auth URL:", error);
      res.status(500).json({ message: "Failed to generate Google auth URL" });
    }
  });

  app.get("/api/auth/google/callback", requireAuth, async (req, res) => {
    try {
      const { code, state } = req.query;

      if (!code) {
        console.error("OAuth callback missing authorization code");
        return res.redirect("/platforms?error=missing_code");
      }

      // Exchange code for tokens
      const tokens = await googleBusinessAPI.getTokensFromCode(code as string);

      if (!tokens.access_token) {
        console.error("Failed to obtain access token from Google");
        return res.redirect("/platforms?error=token_exchange_failed");
      }

      // Store tokens in database
      const hotelId = Number(req.user?.hotelId) || 1;
      await storage.savePlatformToken({
        hotelId,
        platform: "google",
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token || null,
        expiresAt: tokens.expiry_date || null,
      });

      console.log(`Successfully connected Google Business Profile for hotel ${hotelId}`);

      // Redirect to platforms page with success message
      res.redirect("/platforms?connected=google");
    } catch (error) {
      console.error("Error in Google OAuth callback:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.redirect(`/platforms?error=google_auth_failed&details=${encodeURIComponent(errorMessage)}`);
    }
  });

  // Google Business data routes
  app.get("/api/google/accounts", requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.user?.hotelId) || 1;
      const token = await storage.getPlatformToken(hotelId, "google");

      if (!token) {
        return res.status(401).json({ message: "Google account not connected" });
      }

      // Set credentials and get accounts
      googleBusinessAPI.setCredentials({
        access_token: token.accessToken,
        refresh_token: token.refreshToken || undefined,
        expiry_date: token.expiresAt || undefined,
      });

      const accounts = await googleBusinessAPI.getAccounts();
      res.json(accounts);
    } catch (error) {
      console.error("Error fetching Google accounts:", error);
      res.status(500).json({ message: "Failed to fetch Google Business accounts" });
    }
  });

  app.get("/api/google/locations", requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.user?.hotelId) || 1;
      const accountName = req.query.accountName as string;

      if (!accountName) {
        return res.status(400).json({ message: "Account name is required" });
      }

      const token = await storage.getPlatformToken(hotelId, "google");

      if (!token) {
        return res.status(401).json({ message: "Google account not connected" });
      }

      // Set credentials and get locations
      googleBusinessAPI.setCredentials({
        access_token: token.accessToken,
        refresh_token: token.refreshToken || undefined,
        expiry_date: token.expiresAt || undefined,
      });

      const locations = await googleBusinessAPI.getLocations(accountName);
      res.json(locations);
    } catch (error) {
      console.error("Error fetching Google locations:", error);
      res.status(500).json({ message: "Failed to fetch Google Business locations" });
    }
  });

  app.post("/api/google/sync-reviews", strictRateLimit, requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.user?.hotelId) || 1;
      const { locationName } = req.body;

      if (!locationName) {
        return res.status(400).json({ message: "Location name is required" });
      }

      const token = await storage.getPlatformToken(hotelId, "google");

      if (!token) {
        return res.status(401).json({ message: "Google account not connected" });
      }

      // Set credentials
      googleBusinessAPI.setCredentials({
        access_token: token.accessToken,
        refresh_token: token.refreshToken || undefined,
        expiry_date: token.expiresAt || undefined,
      });

      // Fetch reviews from Google
      const { reviews: googleReviews } = await googleBusinessAPI.getReviews(locationName);

      let syncedCount = 0;
      let skippedCount = 0;

      // Process each review
      for (const googleReview of googleReviews) {
        // Check if review already exists
        const existingReview = await storage.getReviewByExternalId("google", googleReview.reviewId);

        if (!existingReview) {
          // Convert and save new review
          const reviewData = GoogleBusinessAPI.convertToInternalReview(googleReview, hotelId);
          await storage.saveReview(reviewData);
          syncedCount++;
        } else {
          skippedCount++;
        }
      }

      res.json({
        message: "Reviews synced successfully",
        syncedCount,
        skippedCount,
        totalProcessed: googleReviews.length,
      });
    } catch (error) {
      console.error("Error syncing Google reviews:", error);
      res.status(500).json({ message: "Failed to sync Google reviews" });
    }
  });

  // Sync management routes
  app.get("/api/sync/status", requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.user?.hotelId) || 1;
      const syncStatuses = await syncService.getSyncStatuses(hotelId);
      res.json(syncStatuses);
    } catch (error) {
      console.error("Error fetching sync status:", error);
      res.status(500).json({ message: "Failed to fetch sync status" });
    }
  });

  app.post("/api/sync/trigger", requireAuth, async (req, res) => {
    try {
      const { locationId, platform } = req.body;

      if (!locationId || !platform) {
        return res.status(400).json({ message: "Location ID and platform are required" });
      }

      const result = await syncService.triggerSync(Number(locationId), platform);
      res.json(result);
    } catch (error) {
      console.error("Error triggering sync:", error);
      res.status(500).json({ message: "Failed to trigger sync" });
    }
  });

  app.post("/api/sync/configure", requireAuth, async (req, res) => {
    try {
      const { locationId, platform, intervalMinutes, enabled } = req.body;

      if (!locationId || !platform) {
        return res.status(400).json({ message: "Location ID and platform are required" });
      }

      if (enabled) {
        await syncService.addLocationSync(Number(locationId), platform, intervalMinutes || 15);
      } else {
        await syncService.removeLocationSync(Number(locationId), platform);
      }

      res.json({ message: "Sync configuration updated successfully" });
    } catch (error) {
      console.error("Error configuring sync:", error);
      res.status(500).json({ message: "Failed to configure sync" });
    }
  });

  // Platform status endpoint
  app.get("/api/platforms/status", requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.user?.hotelId) || 1;

      // Check connection status for each platform
      const platforms = [
        {
          id: "google",
          name: "Google Business",
          connected: false,
          lastSync: "Never",
          reviewCount: 0,
          error: null as string | null
        },
        {
          id: "tripadvisor",
          name: "TripAdvisor",
          connected: false,
          lastSync: "Never",
          reviewCount: 0,
          error: null as string | null
        },
        {
          id: "booking",
          name: "Booking.com",
          connected: false,
          lastSync: "Never",
          reviewCount: 0,
          error: null as string | null
        },
        {
          id: "airbnb",
          name: "Airbnb",
          connected: false,
          lastSync: "Never",
          reviewCount: 0,
          error: null as string | null
        }
      ];

      // Check Google Business connection
      try {
        const googleToken = await storage.getPlatformToken(hotelId, "google");
        if (googleToken) {
          platforms[0].connected = true;

          // Get review count for Google
          const googleReviews = await storage.listReviews(hotelId, {
            platform: "google",
            limit: 1
          });
          platforms[0].reviewCount = googleReviews.total;

          // Get last sync time (simplified - using most recent review date)
          if (googleReviews.reviews.length > 0) {
            const lastReview = googleReviews.reviews[0];
            platforms[0].lastSync = new Date(lastReview.date).toLocaleString();
          }
        }
      } catch (error) {
        platforms[0].error = "Connection error";
      }

      // TODO: Add similar checks for other platforms when implemented

      res.json(platforms);
    } catch (error) {
      console.error("Error fetching platform statuses:", error);
      res.status(500).json({ message: "Failed to fetch platform statuses" });
    }
  });

  // Platform disconnect endpoint
  app.post("/api/platforms/:platform/disconnect", requireAuth, async (req, res) => {
    try {
      const { platform } = req.params;
      const hotelId = Number(req.user?.hotelId) || 1;

      // Remove platform token from database
      await storage.deletePlatformToken(hotelId, platform);

      res.json({ message: `Successfully disconnected from ${platform}` });
    } catch (error) {
      console.error(`Error disconnecting from ${req.params.platform}:`, error);
      res.status(500).json({ message: `Failed to disconnect from ${req.params.platform}` });
    }
  });

  // Test endpoint to verify Google API setup (no auth required for testing)
  app.get("/api/google/test", async (req, res) => {
    try {
      // Test if Google API credentials are configured
      const authUrl = googleBusinessAPI.getAuthUrl();
      res.json({
        status: "Google API configured successfully",
        hasCredentials: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
        authUrl: authUrl.substring(0, 50) + "...", // Show partial URL for verification
        clientId: process.env.GOOGLE_CLIENT_ID ? process.env.GOOGLE_CLIENT_ID.substring(0, 20) + "..." : "Missing",
        redirectUri: process.env.GOOGLE_REDIRECT_URI
      });
    } catch (error) {
      res.status(500).json({
        status: "Google API configuration error",
        error: error instanceof Error ? error.message : 'Unknown error',
        hasCredentials: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET)
      });
    }
  });

  // Google Business review reply route
  app.post("/api/google/reply/:reviewId", requireAuth, async (req, res) => {
    try {
      const { reviewId } = req.params;
      const { replyText } = req.body;
      const hotelId = Number(req.user?.hotelId) || 1;

      if (!replyText || replyText.trim().length === 0) {
        return res.status(400).json({ message: "Reply text is required" });
      }

      const token = await storage.getPlatformToken(hotelId, "google");

      if (!token) {
        return res.status(401).json({ message: "Google account not connected" });
      }

      // Set credentials
      googleBusinessAPI.setCredentials({
        access_token: token.accessToken,
        refresh_token: token.refreshToken || undefined,
        expiry_date: token.expiresAt || undefined,
      });

      // Attempt to reply to the review
      const result = await googleBusinessAPI.replyToReview(reviewId, replyText.trim());

      // Store the reply in our database regardless of API success
      try {
        const replyData = {
          reviewId: parseInt(reviewId) || 0,
          content: replyText.trim(),
          userId: req.user!.id,
          platform: 'google' as const,
          externalReplyId: result.success ? `google-reply-${Date.now()}` : null,
          status: result.success ? 'posted' as const : 'pending' as const
        };

        const savedReply = await storage.saveReply(replyData);

        res.json({
          success: result.success,
          message: result.message,
          requiresManualProcessing: result.requiresManualProcessing,
          reply: savedReply
        });
      } catch (dbError) {
        console.error('Error saving reply to database:', dbError);
        res.json({
          success: result.success,
          message: result.message + ' (Note: Reply not saved to local database)',
          requiresManualProcessing: result.requiresManualProcessing
        });
      }

    } catch (error) {
      console.error("Error posting Google review reply:", error);
      res.status(500).json({
        success: false,
        message: "Failed to post Google review reply",
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Serve the test page
  app.get("/test-google", (req, res) => {
    res.send(`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Business API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🔍 Google Business API Connection Test</h1>

    <div class="step">
        <h3>Step 1: Test Google API Configuration</h3>
        <button onclick="testGoogleConfig()">Test Google Config</button>
        <div id="google-config-result"></div>
    </div>

    <div class="step">
        <h3>Step 2: Test Authentication Flow</h3>
        <p><strong>Important:</strong> You need to be logged into your hotel dashboard first!</p>
        <button onclick="goToLogin()">Go to Login Page</button>
        <button onclick="goToPlatforms()">Go to Platforms Page</button>
        <div id="auth-result"></div>
    </div>

    <div class="step">
        <h3>Step 3: Manual Google Auth Test</h3>
        <button onclick="testGoogleAuth()">Get Google Auth URL</button>
        <div id="manual-auth-result"></div>
    </div>

    <div class="test-section">
        <h3>Debug Information</h3>
        <div id="debug-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Server:</strong> ${req.get('host')}</p>
        </div>
    </div>

    <script>
        document.getElementById('current-url').textContent = window.location.href;

        async function testGoogleConfig() {
            const resultDiv = document.getElementById('google-config-result');
            resultDiv.innerHTML = '<p class="info">Testing Google API configuration...</p>';

            try {
                const response = await fetch('/api/google/test');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = \`
                        <p class="success">✅ Google API configured successfully</p>
                        <pre>\${JSON.stringify(data, null, 2)}</pre>
                    \`;
                } else {
                    resultDiv.innerHTML = \`
                        <p class="error">❌ Google API configuration error</p>
                        <pre>\${JSON.stringify(data, null, 2)}</pre>
                    \`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<p class="error">❌ Failed to test Google config: \${error.message}</p>\`;
            }
        }

        function goToLogin() {
            window.open('/auth', '_blank');
        }

        function goToPlatforms() {
            window.open('/platforms', '_blank');
        }

        async function testGoogleAuth() {
            const resultDiv = document.getElementById('manual-auth-result');
            resultDiv.innerHTML = '<p class="info">Getting Google Auth URL...</p>';

            try {
                const response = await fetch('/api/auth/google');
                const data = await response.json();

                if (response.ok && data.authUrl) {
                    resultDiv.innerHTML = \`
                        <p class="success">✅ Auth URL generated successfully</p>
                        <p><a href="\${data.authUrl}" target="_blank" style="color: #007bff; font-weight: bold;">🔗 Click here to authenticate with Google</a></p>
                        <p class="info">⚠️ Note: This will only work if you're logged into your hotel dashboard</p>
                        <details>
                            <summary>Full Auth URL</summary>
                            <pre>\${data.authUrl}</pre>
                        </details>
                    \`;
                } else {
                    resultDiv.innerHTML = \`
                        <p class="error">❌ Failed to generate auth URL</p>
                        <pre>\${JSON.stringify(data, null, 2)}</pre>
                        <p class="info">💡 This is expected if you're not logged in</p>
                    \`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<p class="error">❌ Auth flow failed: \${error.message}</p>\`;
            }
        }

        // Auto-test on page load
        window.onload = function() {
            testGoogleConfig();
        };
    </script>
</body>
</html>
    `);
  });

  // Statistics route
  app.get("/api/statistics", requireAuth, async (req, res) => {
    try {
      const hotelId = Number(req.query.hotelId) || 1; // Default to hotel 1 for MVP
      
      const { reviews, total } = await storage.listReviews(hotelId);
      
      // Calculate statistics
      const totalReviews = total;
      const repliedCount = reviews.filter(r => r.isReplied).length;
      const pendingReplies = totalReviews - repliedCount;
      
      // Calculate average rating
      let totalRating = 0;
      reviews.forEach(r => {
        totalRating += r.rating;
      });
      const averageRating = totalReviews > 0 ? (totalRating / totalReviews).toFixed(1) : "0";
      
      // Platform breakdown
      const platformCounts = {
        google: reviews.filter(r => r.platform === "google").length,
        booking: reviews.filter(r => r.platform === "booking").length,
        airbnb: reviews.filter(r => r.platform === "airbnb").length,
        tripadvisor: reviews.filter(r => r.platform === "tripadvisor").length
      };
      
      res.json({
        totalReviews,
        pendingReplies,
        averageRating,
        platformCounts
      });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch statistics" });
    }
  });

  const httpServer = createServer(app);

  // Initialize WebSocket service
  const webSocketService = initializeWebSocket(httpServer);
  console.log('WebSocket service initialized');

  // Start sync service
  syncService.start().catch(error => {
    console.error('Failed to start sync service:', error);
  });

  return httpServer;
}
