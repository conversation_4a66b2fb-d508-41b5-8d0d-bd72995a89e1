{"version": 3, "sources": ["../src/index.ts", "../src/create-context.tsx"], "sourcesContent": ["export { createContext, createContextScope } from './create-context';\nexport type { CreateScope, Scope } from './create-context';\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  const Provider: React.FC<ContextValueType & { children: React.ReactNode }> = (props) => {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  };\n\n  Provider.displayName = rootComponentName + 'Provider';\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    const Provider: React.FC<\n      ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    > = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    };\n\n    Provider.displayName = rootComponentName + 'Provider';\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,uBAAAA;AAAA,EAAA;AAAA;AAAA;;;ACAA,YAAuB;AAaZ;AAXX,SAASC,eACP,mBACA,gBACA;AACA,QAAM,UAAgB,oBAA4C,cAAc;AAEhF,QAAM,WAAuE,CAAC,UAAU;AACtF,UAAM,EAAE,UAAU,GAAG,QAAQ,IAAI;AAGjC,UAAM,QAAc,cAAQ,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,WAAO,4CAAC,QAAQ,UAAR,EAAiB,OAAe,UAAS;AAAA,EACnD;AAEA,WAAS,cAAc,oBAAoB;AAE3C,WAASC,YAAW,cAAsB;AACxC,UAAM,UAAgB,iBAAW,OAAO;AACxC,QAAI,QAAS,QAAO;AACpB,QAAI,mBAAmB,OAAW,QAAO;AAEzC,UAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;AAAA,EACpF;AAEA,SAAO,CAAC,UAAUA,WAAU;AAC9B;AAaA,SAAS,mBAAmB,WAAmB,yBAAwC,CAAC,GAAG;AACzF,MAAI,kBAAyB,CAAC;AAM9B,WAASD,eACP,mBACA,gBACA;AACA,UAAM,cAAoB,oBAA4C,cAAc;AACpF,UAAM,QAAQ,gBAAgB;AAC9B,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AAErD,UAAM,WAEF,CAAC,UAAU;AACb,YAAM,EAAE,OAAO,UAAU,GAAG,QAAQ,IAAI;AACxC,YAAM,UAAU,QAAQ,SAAS,IAAI,KAAK,KAAK;AAG/C,YAAM,QAAc,cAAQ,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,aAAO,4CAAC,QAAQ,UAAR,EAAiB,OAAe,UAAS;AAAA,IACnD;AAEA,aAAS,cAAc,oBAAoB;AAE3C,aAASC,YAAW,cAAsB,OAA4C;AACpF,YAAM,UAAU,QAAQ,SAAS,IAAI,KAAK,KAAK;AAC/C,YAAM,UAAgB,iBAAW,OAAO;AACxC,UAAI,QAAS,QAAO;AACpB,UAAI,mBAAmB,OAAW,QAAO;AAEzC,YAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;AAAA,IACpF;AAEA,WAAO,CAAC,UAAUA,WAAU;AAAA,EAC9B;AAMA,QAAM,cAA2B,MAAM;AACrC,UAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,aAAa,oBAAc,cAAc;AAAA,IAC3C,CAAC;AACD,WAAO,SAAS,SAAS,OAAc;AACrC,YAAM,WAAW,QAAQ,SAAS,KAAK;AACvC,aAAa;AAAA,QACX,OAAO,EAAE,CAAC,UAAU,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,GAAG,SAAS,EAAE;AAAA,QACtE,CAAC,OAAO,QAAQ;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AAEA,cAAY,YAAY;AACxB,SAAO,CAACD,gBAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;AACrF;AAMA,SAAS,wBAAwB,QAAuB;AACtD,QAAM,YAAY,OAAO,CAAC;AAC1B,MAAI,OAAO,WAAW,EAAG,QAAO;AAEhC,QAAM,cAA2B,MAAM;AACrC,UAAM,aAAa,OAAO,IAAI,CAACE,kBAAiB;AAAA,MAC9C,UAAUA,aAAY;AAAA,MACtB,WAAWA,aAAY;AAAA,IACzB,EAAE;AAEF,WAAO,SAAS,kBAAkB,gBAAgB;AAChD,YAAM,aAAa,WAAW,OAAO,CAACC,aAAY,EAAE,UAAU,UAAU,MAAM;AAI5E,cAAM,aAAa,SAAS,cAAc;AAC1C,cAAM,eAAe,WAAW,UAAU,SAAS,EAAE;AACrD,eAAO,EAAE,GAAGA,aAAY,GAAG,aAAa;AAAA,MAC1C,GAAG,CAAC,CAAC;AAEL,aAAa,cAAQ,OAAO,EAAE,CAAC,UAAU,UAAU,SAAS,EAAE,GAAG,WAAW,IAAI,CAAC,UAAU,CAAC;AAAA,IAC9F;AAAA,EACF;AAEA,cAAY,YAAY,UAAU;AAClC,SAAO;AACT;", "names": ["createContext", "createContext", "useContext", "createScope", "nextScopes"]}