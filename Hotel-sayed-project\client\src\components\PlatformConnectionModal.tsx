import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaGoogle, FaBuilding, FaAirbnb, FaTripadvisor, FaPlug } from "react-icons/fa";

interface PlatformConnectionModalProps {
  isOpen: boolean;
  onClose: (success?: boolean) => void;
  platformId?: string | null;
}

export default function PlatformConnectionModal({ isOpen, onClose, platformId }: PlatformConnectionModalProps) {
  const handleConnectPlatform = async (platform: string) => {
    try {
      if (platform === "google") {
        // Redirect to Google OAuth flow
        const response = await fetch("/api/auth/google", {
          credentials: "include"
        });

        if (response.ok) {
          const data = await response.json();
          // Redirect to Google OAuth URL
          window.location.href = data.authUrl;
        } else {
          throw new Error("Failed to initiate Google OAuth");
        }
      } else {
        // For other platforms, show not implemented message
        console.log(`${platform} integration not yet implemented`);
        onClose(false);
      }
    } catch (error) {
      console.error(`Error connecting to ${platform}:`, error);
      onClose(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-100">
            <FaPlug className="text-primary text-lg" />
          </div>
          <DialogTitle className="text-center pt-4">Connect New Platform</DialogTitle>
          <DialogDescription className="text-center">
            Select a platform to connect and follow the authentication steps to import reviews from that platform.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 space-y-3">
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("google")}
          >
            <FaGoogle className="text-[#4285F4] mr-2 text-lg" />
            Connect with Google Business
          </Button>

          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("booking")}
            disabled
          >
            <FaBuilding className="text-[#003580] mr-2 text-lg" />
            Connect with Booking.com (Coming Soon)
          </Button>

          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("airbnb")}
            disabled
          >
            <FaAirbnb className="text-[#FF5A5F] mr-2 text-lg" />
            Connect with Airbnb (Coming Soon)
          </Button>

          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => handleConnectPlatform("tripadvisor")}
            disabled
          >
            <FaTripadvisor className="text-[#00AA6C] mr-2 text-lg" />
            Connect with TripAdvisor (Coming Soon)
          </Button>
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={() => onClose()}
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
