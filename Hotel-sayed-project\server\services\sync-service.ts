import { storage } from '../storage';
import { googleBusinessAPI, GoogleBusinessAPI } from './google-business-api';
import { tripAdvisorAPI, TripAdvisorAPI } from './tripadvisor-api';
import { bookingAPI, BookingAPI } from './booking-api';
import { airbnbAPI, AirbnbAPI } from './airbnb-api';
import { EventEmitter } from 'events';

export interface SyncResult {
  locationId: number;
  platform: string;
  success: boolean;
  newReviews: number;
  totalReviews: number;
  error?: string;
  syncTime: number;
}

export class SyncService extends EventEmitter {
  private syncIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning = false;
  private activeSyncs: Map<string, Promise<SyncResult>> = new Map();
  private syncQueue: Array<{ locationId: number; platform: string; priority: number; retryCount: number }> = [];
  private maxConcurrentSyncs = 3;
  private currentSyncs = 0;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.setupEventHandlers();
    this.setupErrorRecovery();
  }

  private setupEventHandlers() {
    this.on('syncComplete', (result: SyncResult) => {
      console.log(`✅ Sync completed for location ${result.locationId} (${result.platform}): ${result.newReviews} new reviews`);
      this.currentSyncs = Math.max(0, this.currentSyncs - 1);
      this.processQueue();
    });

    this.on('syncError', (result: SyncResult) => {
      console.error(`❌ Sync failed for location ${result.locationId} (${result.platform}): ${result.error}`);
      this.currentSyncs = Math.max(0, this.currentSyncs - 1);
      this.handleSyncError(result);
      this.processQueue();
    });
  }

  /**
   * Setup error recovery mechanisms
   */
  private setupErrorRecovery(): void {
    // Periodic health check every 5 minutes
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 5 * 60 * 1000);

    // Process queue every 30 seconds
    setInterval(() => {
      this.processQueue();
    }, 30 * 1000);
  }

  /**
   * Process the sync queue
   */
  private async processQueue(): Promise<void> {
    if (this.currentSyncs >= this.maxConcurrentSyncs || this.syncQueue.length === 0) {
      return;
    }

    // Sort queue by priority (higher priority first)
    this.syncQueue.sort((a, b) => b.priority - a.priority);

    const queueItem = this.syncQueue.shift();
    if (!queueItem) return;

    const key = `${queueItem.locationId}-${queueItem.platform}`;

    // Check if this sync is already running
    if (this.activeSyncs.has(key)) {
      return;
    }

    this.currentSyncs++;
    console.log(`🔄 Starting queued sync for location ${queueItem.locationId} (${queueItem.platform}), retry: ${queueItem.retryCount}`);

    const syncPromise = this.syncLocation(queueItem.locationId, queueItem.platform);
    this.activeSyncs.set(key, syncPromise);

    try {
      await syncPromise;
    } finally {
      this.activeSyncs.delete(key);
    }
  }

  /**
   * Handle sync errors with retry logic
   */
  private handleSyncError(result: SyncResult): void {
    const maxRetries = 3;
    const queueItem = this.syncQueue.find(
      item => item.locationId === result.locationId && item.platform === result.platform
    );

    const retryCount = queueItem?.retryCount || 0;

    if (retryCount < maxRetries) {
      // Add back to queue with lower priority and incremented retry count
      this.syncQueue.push({
        locationId: result.locationId,
        platform: result.platform,
        priority: 1, // Lower priority for retries
        retryCount: retryCount + 1
      });

      console.log(`🔄 Queuing retry ${retryCount + 1}/${maxRetries} for location ${result.locationId} (${result.platform})`);
    } else {
      console.error(`💀 Max retries exceeded for location ${result.locationId} (${result.platform})`);

      // Emit a final error event
      this.emit('syncMaxRetriesExceeded', {
        ...result,
        retryCount: maxRetries
      });
    }
  }

  /**
   * Perform health check on sync service
   */
  private async performHealthCheck(): Promise<void> {
    try {
      console.log(`🏥 Health check: ${this.currentSyncs} active syncs, ${this.syncQueue.length} queued`);

      // Check for stuck syncs (running for more than 10 minutes)
      const stuckSyncThreshold = 10 * 60 * 1000; // 10 minutes
      const now = Date.now();

      for (const [key, syncPromise] of this.activeSyncs.entries()) {
        // This is a simplified check - in a real implementation you'd track start times
        console.log(`Active sync: ${key}`);
      }

      // Clean up completed intervals
      this.cleanupCompletedIntervals();

    } catch (error) {
      console.error('Health check failed:', error);
    }
  }

  /**
   * Clean up completed sync intervals
   */
  private cleanupCompletedIntervals(): void {
    // This would be enhanced to track and clean up stale intervals
    console.log(`📊 Managing ${this.syncIntervals.size} sync intervals`);
  }

  /**
   * Start the sync service for all active locations
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Sync service is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting sync service...');

    try {
      // Get all sync statuses
      const syncStatuses = await storage.listSyncStatuses();
      
      for (const syncStatus of syncStatuses) {
        if (syncStatus.isEnabled) {
          await this.scheduleLocationSync(
            syncStatus.locationId,
            syncStatus.platform,
            syncStatus.syncIntervalMinutes || 15
          );
        }
      }

      console.log(`Sync service started for ${syncStatuses.length} locations`);
    } catch (error) {
      console.error('Failed to start sync service:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Stop the sync service with proper cleanup
   */
  stop(): void {
    console.log('🛑 Stopping sync service...');

    // Clear all intervals
    this.syncIntervals.forEach((interval, key) => {
      clearInterval(interval);
      console.log(`Cleared sync interval for ${key}`);
    });
    this.syncIntervals.clear();

    // Clear health check interval
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }

    // Clear sync queue
    this.syncQueue.length = 0;

    // Wait for active syncs to complete (with timeout)
    const activeCount = this.activeSyncs.size;
    if (activeCount > 0) {
      console.log(`⏳ Waiting for ${activeCount} active syncs to complete...`);

      // Give active syncs 30 seconds to complete
      setTimeout(() => {
        if (this.activeSyncs.size > 0) {
          console.warn(`⚠️ Force stopping with ${this.activeSyncs.size} active syncs remaining`);
          this.activeSyncs.clear();
        }
      }, 30000);
    }

    this.isRunning = false;
    this.currentSyncs = 0;
    console.log('✅ Sync service stopped');
  }

  /**
   * Schedule sync for a specific location with enhanced scheduling
   */
  private async scheduleLocationSync(locationId: number, platform: string, intervalMinutes: number): Promise<void> {
    const key = `${locationId}-${platform}`;

    // Clear existing interval if any
    if (this.syncIntervals.has(key)) {
      clearInterval(this.syncIntervals.get(key)!);
    }

    // Add initial sync to queue with high priority
    this.addToQueue(locationId, platform, 10, 0);

    // Schedule recurring sync with intelligent timing
    const baseInterval = intervalMinutes * 60 * 1000; // Convert minutes to milliseconds

    // Add some jitter to prevent all syncs from running at the same time
    const jitter = Math.random() * 0.1 * baseInterval; // Up to 10% jitter
    const actualInterval = baseInterval + jitter;

    const interval = setInterval(async () => {
      // Check if we should skip this sync based on recent activity
      const shouldSkip = await this.shouldSkipSync(locationId, platform);
      if (shouldSkip) {
        console.log(`⏭️ Skipping scheduled sync for location ${locationId} (${platform}) - recent activity detected`);
        return;
      }

      // Add to queue instead of direct sync to respect concurrency limits
      this.addToQueue(locationId, platform, 5, 0); // Medium priority for scheduled syncs
    }, actualInterval);

    this.syncIntervals.set(key, interval);
    console.log(`📅 Scheduled sync for location ${locationId} (${platform}) every ${intervalMinutes} minutes (with ${Math.round(jitter/1000)}s jitter)`);
  }

  /**
   * Add sync to queue
   */
  private addToQueue(locationId: number, platform: string, priority: number, retryCount: number): void {
    // Check if already in queue
    const existingIndex = this.syncQueue.findIndex(
      item => item.locationId === locationId && item.platform === platform
    );

    if (existingIndex >= 0) {
      // Update priority if higher
      if (this.syncQueue[existingIndex].priority < priority) {
        this.syncQueue[existingIndex].priority = priority;
      }
      return;
    }

    this.syncQueue.push({ locationId, platform, priority, retryCount });
    console.log(`📋 Added to sync queue: location ${locationId} (${platform}), priority: ${priority}, queue size: ${this.syncQueue.length}`);

    // Trigger queue processing
    setImmediate(() => this.processQueue());
  }

  /**
   * Check if sync should be skipped based on recent activity
   */
  private async shouldSkipSync(locationId: number, platform: string): Promise<boolean> {
    try {
      const syncStatus = await storage.getSyncStatus(locationId, platform);
      if (!syncStatus) return false;

      const now = Date.now();
      const lastSync = syncStatus.lastSyncAt || 0;
      const timeSinceLastSync = now - lastSync;

      // Skip if synced within the last 5 minutes
      if (timeSinceLastSync < 5 * 60 * 1000) {
        return true;
      }

      // Skip if currently in progress
      if (syncStatus.lastSyncStatus === 'in_progress') {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking sync status:', error);
      return false;
    }
  }

  /**
   * Sync reviews for a specific location with enhanced error handling
   */
  async syncLocation(locationId: number, platform: string): Promise<SyncResult> {
    const syncTime = Date.now();
    const result: SyncResult = {
      locationId,
      platform,
      success: false,
      newReviews: 0,
      totalReviews: 0,
      syncTime,
    };

    console.log(`🔄 Starting sync for location ${locationId} (${platform})`);

    try {
      // Emit sync start event for real-time updates
      this.emit('syncStarted', {
        locationId,
        platform,
        syncTime
      });

      // Update sync status to in_progress
      await storage.updateSyncStatus(locationId, platform, {
        lastSyncStatus: 'in_progress',
        updatedAt: syncTime,
      });

      if (platform === 'google') {
        const syncResult = await this.syncGoogleLocation(locationId);
        Object.assign(result, syncResult);
      } else if (platform === 'tripadvisor') {
        const syncResult = await this.syncTripAdvisorLocation(locationId);
        Object.assign(result, syncResult);
      } else if (platform === 'booking') {
        const syncResult = await this.syncBookingLocation(locationId);
        Object.assign(result, syncResult);
      } else if (platform === 'airbnb') {
        const syncResult = await this.syncAirbnbLocation(locationId);
        Object.assign(result, syncResult);
      } else {
        throw new Error(`Platform ${platform} not supported`);
      }

      // Calculate next sync time with adaptive interval
      const adaptiveInterval = this.calculateAdaptiveInterval(result.newReviews, platform);

      // Update sync status to success
      await storage.updateSyncStatus(locationId, platform, {
        lastSyncAt: syncTime,
        lastSyncStatus: 'success',
        lastSyncError: null,
        reviewsCount: result.totalReviews,
        newReviewsCount: result.newReviews,
        nextSyncAt: syncTime + adaptiveInterval,
        updatedAt: syncTime,
      });

      result.success = true;

      // Emit detailed completion event
      this.emit('syncComplete', {
        ...result,
        duration: Date.now() - syncTime,
        nextSyncIn: adaptiveInterval
      });

      // If new reviews found, emit special event
      if (result.newReviews > 0) {
        this.emit('newReviewsFound', {
          locationId,
          platform,
          count: result.newReviews,
          syncTime
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.error = errorMessage;

      console.error(`❌ Sync failed for location ${locationId} (${platform}):`, errorMessage);

      // Update sync status to error
      await storage.updateSyncStatus(locationId, platform, {
        lastSyncStatus: 'error',
        lastSyncError: errorMessage,
        updatedAt: syncTime,
        nextSyncAt: syncTime + (30 * 60 * 1000), // Retry in 30 minutes on error
      });

      this.emit('syncError', {
        ...result,
        duration: Date.now() - syncTime
      });
    }

    return result;
  }

  /**
   * Calculate adaptive sync interval based on activity
   */
  private calculateAdaptiveInterval(newReviews: number, platform: string): number {
    const baseInterval = 15 * 60 * 1000; // 15 minutes base

    if (newReviews > 5) {
      // High activity - sync more frequently
      return 5 * 60 * 1000; // 5 minutes
    } else if (newReviews > 0) {
      // Some activity - normal frequency
      return baseInterval; // 15 minutes
    } else {
      // No activity - sync less frequently
      return 30 * 60 * 1000; // 30 minutes
    }
  }

  /**
   * Sync Google Business location reviews
   */
  private async syncGoogleLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get Google Business location
    const location = await storage.getGoogleBusinessLocation(locationId);
    if (!location) {
      throw new Error(`Google Business location ${locationId} not found`);
    }

    // Get platform token
    const token = await storage.getPlatformToken(location.hotelId, 'google');
    if (!token) {
      throw new Error(`Google platform token not found for hotel ${location.hotelId}`);
    }

    // Set up Google API credentials
    googleBusinessAPI.setCredentials({
      access_token: token.accessToken,
      refresh_token: token.refreshToken || undefined,
      expiry_date: token.expiresAt || undefined,
    });

    // Fetch reviews from Google
    const { reviews: googleReviews } = await googleBusinessAPI.getReviews(location.googleLocationName);
    
    let newReviews = 0;
    
    // Process each review
    for (const googleReview of googleReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('google', googleReview.reviewId);
      
      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = GoogleBusinessAPI.convertToInternalReview(googleReview, location.hotelId);
        const reviewData = {
          ...baseReviewData,
          googleLocationId: locationId,
          googleReviewName: googleReview.name,
          updateTime: new Date(googleReview.updateTime).getTime(),
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: googleReviews.length,
    };
  }

  /**
   * Sync TripAdvisor location reviews
   */
  private async syncTripAdvisorLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get TripAdvisor location
    const location = await storage.getTripAdvisorLocation(locationId);
    if (!location) {
      throw new Error(`TripAdvisor location ${locationId} not found`);
    }

    // Fetch reviews from TripAdvisor
    const { reviews: tripAdvisorReviews } = await tripAdvisorAPI.getReviews(location.tripAdvisorLocationId);

    let newReviews = 0;

    // Process each review
    for (const tripAdvisorReview of tripAdvisorReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('tripadvisor', tripAdvisorReview.id);

      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = TripAdvisorAPI.convertToInternalReview(tripAdvisorReview, location.hotelId);
        const reviewData = {
          ...baseReviewData,
          tripAdvisorLocationId: location.tripAdvisorLocationId,
          tripAdvisorUrl: tripAdvisorReview.url,
          tripType: tripAdvisorReview.trip_type,
          travelDate: tripAdvisorReview.travel_date,
          helpfulVotes: tripAdvisorReview.helpful_votes,
          hasManagementResponse: tripAdvisorReview.management_response ? 1 : 0,
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: tripAdvisorReviews.length,
    };
  }

  /**
   * Sync Booking.com property reviews
   */
  private async syncBookingLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get Booking.com property
    const property = await storage.getBookingProperty(locationId);
    if (!property) {
      throw new Error(`Booking.com property ${locationId} not found`);
    }

    // Fetch reviews from Booking.com
    const { reviews: bookingReviews } = await bookingAPI.getReviews(property.bookingHotelId);

    let newReviews = 0;

    // Process each review
    for (const bookingReview of bookingReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('booking', bookingReview.review_id);

      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = BookingAPI.convertToInternalReview(bookingReview, property.hotelId);
        const reviewData = {
          ...baseReviewData,
          bookingHotelId: bookingReview.hotel_id,
          reviewerCountry: bookingReview.reviewer_country,
          roomType: bookingReview.room_type,
          groupType: bookingReview.group_type,
          stayedNights: bookingReview.stayed_nights,
          isVerified: bookingReview.is_verified ? 1 : 0,
          helpfulVotes: bookingReview.helpful_votes,
          hasManagementResponse: bookingReview.management_response ? 1 : 0,
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: bookingReviews.length,
    };
  }

  /**
   * Sync Airbnb listing reviews
   */
  private async syncAirbnbLocation(locationId: number): Promise<Partial<SyncResult>> {
    // Get Airbnb listing
    const listing = await storage.getAirbnbListing(locationId);
    if (!listing) {
      throw new Error(`Airbnb listing ${locationId} not found`);
    }

    // Fetch reviews from Airbnb
    const { reviews: airbnbReviews } = await airbnbAPI.getReviews(listing.airbnbListingId);

    let newReviews = 0;

    // Process each review
    for (const airbnbReview of airbnbReviews) {
      // Check if review already exists
      const existingReview = await storage.getReviewByExternalId('airbnb', airbnbReview.id);

      if (!existingReview) {
        // Convert and save new review
        const baseReviewData = AirbnbAPI.convertToInternalReview(airbnbReview, listing.hotelId);
        const reviewData = {
          ...baseReviewData,
          airbnbListingId: airbnbReview.listing_id,
          reviewerId: airbnbReview.reviewer_id,
          language: airbnbReview.language,
          privateFeedback: airbnbReview.private_feedback,
          hasHostResponse: airbnbReview.response_from_host ? 1 : 0,
          lastSyncAt: Date.now(),
        };

        await storage.saveReview(reviewData);
        newReviews++;
      }
    }

    return {
      newReviews,
      totalReviews: airbnbReviews.length,
    };
  }

  /**
   * Manually trigger sync for a specific location with high priority
   */
  async triggerSync(locationId: number, platform: string): Promise<SyncResult> {
    console.log(`🚀 Manually triggering high-priority sync for location ${locationId} (${platform})`);

    // Add to queue with highest priority
    this.addToQueue(locationId, platform, 20, 0);

    // Wait for the sync to complete by monitoring the active syncs
    const key = `${locationId}-${platform}`;

    // If already running, wait for it
    if (this.activeSyncs.has(key)) {
      return await this.activeSyncs.get(key)!;
    }

    // Otherwise, process the queue and wait for our sync
    await this.processQueue();

    // Return a promise that resolves when our sync completes
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Manual sync timeout after 5 minutes'));
      }, 5 * 60 * 1000);

      const onComplete = (result: SyncResult) => {
        if (result.locationId === locationId && result.platform === platform) {
          clearTimeout(timeout);
          this.off('syncComplete', onComplete);
          this.off('syncError', onError);
          resolve(result);
        }
      };

      const onError = (result: SyncResult) => {
        if (result.locationId === locationId && result.platform === platform) {
          clearTimeout(timeout);
          this.off('syncComplete', onComplete);
          this.off('syncError', onError);
          resolve(result); // Still resolve with the error result
        }
      };

      this.on('syncComplete', onComplete);
      this.on('syncError', onError);
    });
  }

  /**
   * Add a new location to sync
   */
  async addLocationSync(locationId: number, platform: string, intervalMinutes: number = 15): Promise<void> {
    // Create or update sync status
    await storage.saveSyncStatus({
      locationId,
      platform,
      syncIntervalMinutes: intervalMinutes,
      isEnabled: 1,
      lastSyncStatus: 'pending',
    });

    // Schedule sync if service is running
    if (this.isRunning) {
      await this.scheduleLocationSync(locationId, platform, intervalMinutes);
    }
  }

  /**
   * Remove a location from sync
   */
  async removeLocationSync(locationId: number, platform: string): Promise<void> {
    const key = `${locationId}-${platform}`;
    
    // Clear interval
    if (this.syncIntervals.has(key)) {
      clearInterval(this.syncIntervals.get(key)!);
      this.syncIntervals.delete(key);
    }

    // Disable sync status
    await storage.updateSyncStatus(locationId, platform, {
      isEnabled: 0,
      updatedAt: Date.now(),
    });
  }

  /**
   * Get sync status for all locations
   */
  async getSyncStatuses(hotelId?: number): Promise<any[]> {
    return await storage.listSyncStatuses(hotelId);
  }
}

// Export singleton instance
export const syncService = new SyncService();
