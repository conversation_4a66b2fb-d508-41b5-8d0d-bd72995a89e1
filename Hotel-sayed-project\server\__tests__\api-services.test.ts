import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { googleBusinessAPI } from '../services/google-business-api';
import { tripAdvisorAPI } from '../services/tripadvisor-api';
import { bookingAPI } from '../services/booking-api';
import { airbnbAPI } from '../services/airbnb-api';

// Mock environment variables
process.env.GOOGLE_CLIENT_ID = 'test-google-client-id';
process.env.GOOGLE_CLIENT_SECRET = 'test-google-client-secret';
process.env.GOOGLE_REDIRECT_URI = 'http://localhost:5000/api/auth/google/callback';

describe('API Services', () => {
  describe('Google Business API', () => {
    it('should generate auth URL', () => {
      const authUrl = googleBusinessAPI.getAuthUrl();
      expect(authUrl).toBeDefined();
      expect(authUrl).toContain('accounts.google.com');
      expect(authUrl).toContain('oauth2');
    });

    it('should convert star rating correctly', () => {
      expect((googleBusinessAPI.constructor as any).convertStarRating('ONE')).toBe(1);
      expect((googleBusinessAPI.constructor as any).convertStarRating('TWO')).toBe(2);
      expect((googleBusinessAPI.constructor as any).convertStarRating('THREE')).toBe(3);
      expect((googleBusinessAPI.constructor as any).convertStarRating('FOUR')).toBe(4);
      expect((googleBusinessAPI.constructor as any).convertStarRating('FIVE')).toBe(5);
      expect((googleBusinessAPI.constructor as any).convertStarRating('INVALID')).toBe(0);
    });

    it('should convert Google review to internal format', () => {
      const googleReview = {
        name: 'accounts/123/locations/456/reviews/789',
        reviewId: 'review-789',
        reviewer: {
          displayName: 'John Doe',
          profilePhotoUrl: 'https://example.com/photo.jpg',
        },
        starRating: 'FIVE' as const,
        comment: 'Great service!',
        createTime: '2024-01-01T12:00:00Z',
        updateTime: '2024-01-01T12:00:00Z',
      };

      const internalReview = (googleBusinessAPI.constructor as any).convertToInternalReview(googleReview, 1);
      
      expect(internalReview.hotelId).toBe(1);
      expect(internalReview.platform).toBe('google');
      expect(internalReview.externalId).toBe('review-789');
      expect(internalReview.authorName).toBe('John Doe');
      expect(internalReview.authorImage).toBe('https://example.com/photo.jpg');
      expect(internalReview.rating).toBe(5);
      expect(internalReview.content).toBe('Great service!');
    });

    it('should get reviews with fallback data when no credentials', async () => {
      const result = await googleBusinessAPI.getReviews('test-location');
      
      expect(result).toBeDefined();
      expect(result.reviews).toBeDefined();
      expect(Array.isArray(result.reviews)).toBe(true);
      expect(result.totalReviewCount).toBeDefined();
    });
  });

  describe('TripAdvisor API', () => {
    it('should return mock data when no API key configured', async () => {
      const locations = await tripAdvisorAPI.searchLocations('Test Hotel');
      
      expect(locations).toBeDefined();
      expect(Array.isArray(locations)).toBe(true);
      expect(locations.length).toBeGreaterThan(0);
      expect(locations[0]).toHaveProperty('location_id');
      expect(locations[0]).toHaveProperty('name');
    });

    it('should get reviews with mock data', async () => {
      const result = await tripAdvisorAPI.getReviews('test-location-id');
      
      expect(result).toBeDefined();
      expect(result.reviews).toBeDefined();
      expect(Array.isArray(result.reviews)).toBe(true);
      expect(result.totalResults).toBeDefined();
      expect(result.hasMore).toBeDefined();
    });

    it('should convert TripAdvisor review to internal format', () => {
      const tripAdvisorReview = {
        id: 'ta-review-123',
        lang: 'en',
        location_id: 'ta-location-456',
        published_date: '2024-01-01T12:00:00Z',
        rating: 5,
        helpful_votes: 3,
        rating_image_url: 'https://example.com/rating.svg',
        url: 'https://tripadvisor.com/review-123',
        trip_type: 'Business',
        travel_date: '2023-12-15T00:00:00Z',
        text: 'Excellent hotel with great service!',
        title: 'Outstanding Experience',
        user: {
          user_id: 'user-123',
          member_id: 'member-123',
          type: 'user',
          first_name: 'John',
          last_name: 'Doe',
          avatar: {
            thumbnail: 'https://example.com/thumb.jpg',
            small: 'https://example.com/small.jpg',
            medium: 'https://example.com/medium.jpg',
            large: 'https://example.com/large.jpg',
            original: 'https://example.com/original.jpg',
          },
        },
        is_machine_translated: false,
      };

      const internalReview = (tripAdvisorAPI.constructor as any).convertToInternalReview(tripAdvisorReview, 1);
      
      expect(internalReview.hotelId).toBe(1);
      expect(internalReview.platform).toBe('tripadvisor');
      expect(internalReview.externalId).toBe('ta-review-123');
      expect(internalReview.authorName).toBe('John Doe');
      expect(internalReview.rating).toBe(5);
      expect(internalReview.content).toContain('Outstanding Experience');
      expect(internalReview.content).toContain('Excellent hotel with great service!');
    });
  });

  describe('Booking.com API', () => {
    it('should return mock data when no credentials configured', async () => {
      const properties = await bookingAPI.searchProperties('Test Hotel');
      
      expect(properties).toBeDefined();
      expect(Array.isArray(properties)).toBe(true);
      expect(properties.length).toBeGreaterThan(0);
      expect(properties[0]).toHaveProperty('hotel_id');
      expect(properties[0]).toHaveProperty('name');
    });

    it('should get reviews with mock data', async () => {
      const result = await bookingAPI.getReviews('test-hotel-id');
      
      expect(result).toBeDefined();
      expect(result.reviews).toBeDefined();
      expect(Array.isArray(result.reviews)).toBe(true);
      expect(result.totalResults).toBeDefined();
      expect(result.hasMore).toBeDefined();
    });

    it('should convert Booking.com review to internal format', () => {
      const bookingReview = {
        review_id: 'booking-review-123',
        hotel_id: 'booking-hotel-456',
        reviewer_name: 'Jane Smith',
        reviewer_country: 'United States',
        review_date: '2024-01-01T12:00:00Z',
        review_score: 9.0,
        review_title: 'Excellent Stay',
        review_positive: 'Great location and friendly staff.',
        review_negative: 'WiFi could be faster.',
        room_type: 'Deluxe Room',
        trip_type: 'Leisure',
        group_type: 'Couple',
        stayed_nights: 3,
        language: 'en',
        is_verified: true,
        helpful_votes: 5,
      };

      const internalReview = (bookingAPI.constructor as any).convertToInternalReview(bookingReview, 1);
      
      expect(internalReview.hotelId).toBe(1);
      expect(internalReview.platform).toBe('booking');
      expect(internalReview.externalId).toBe('booking-review-123');
      expect(internalReview.authorName).toBe('Jane Smith');
      expect(internalReview.rating).toBe(9);
      expect(internalReview.content).toContain('Excellent Stay');
      expect(internalReview.content).toContain('Great location and friendly staff');
      expect(internalReview.content).toContain('WiFi could be faster');
    });
  });

  describe('Airbnb API', () => {
    it('should return mock data when no access token configured', async () => {
      const listings = await airbnbAPI.searchListings('Test City');
      
      expect(listings).toBeDefined();
      expect(Array.isArray(listings)).toBe(true);
      expect(listings.length).toBeGreaterThan(0);
      expect(listings[0]).toHaveProperty('id');
      expect(listings[0]).toHaveProperty('name');
    });

    it('should get reviews with mock data', async () => {
      const result = await airbnbAPI.getReviews('test-listing-id');
      
      expect(result).toBeDefined();
      expect(result.reviews).toBeDefined();
      expect(Array.isArray(result.reviews)).toBe(true);
      expect(result.totalResults).toBeDefined();
      expect(result.hasMore).toBeDefined();
    });

    it('should convert Airbnb review to internal format', () => {
      const airbnbReview = {
        id: 'airbnb-review-123',
        listing_id: 'airbnb-listing-456',
        reviewer_id: 'reviewer-789',
        reviewer_name: 'Mike Johnson',
        date: '2024-01-01T12:00:00Z',
        comments: 'Amazing place with great host!',
        language: 'en',
        rating: 5,
      };

      const internalReview = (airbnbAPI.constructor as any).convertToInternalReview(airbnbReview, 1);
      
      expect(internalReview.hotelId).toBe(1);
      expect(internalReview.platform).toBe('airbnb');
      expect(internalReview.externalId).toBe('airbnb-review-123');
      expect(internalReview.authorName).toBe('Mike Johnson');
      expect(internalReview.rating).toBe(5);
      expect(internalReview.content).toBe('Amazing place with great host!');
    });
  });
});
