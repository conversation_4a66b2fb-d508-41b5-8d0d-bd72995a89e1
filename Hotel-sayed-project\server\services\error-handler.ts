import { EventEmitter } from 'events';

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  API_ERROR = 'api_error',
  DATABASE_ERROR = 'database_error',
  AUTHENTICATION_ERROR = 'authentication_error',
  VALIDATION_ERROR = 'validation_error',
  SYNC_ERROR = 'sync_error',
  NETWORK_ERROR = 'network_error',
  RATE_LIMIT_ERROR = 'rate_limit_error',
  GOOGLE_API_ERROR = 'google_api_error',
  GOOGLE_AUTH_ERROR = 'google_auth_error',
  GOOGLE_QUOTA_ERROR = 'google_quota_error',
  UNKNOWN_ERROR = 'unknown_error'
}

export interface ErrorDetails {
  id: string;
  timestamp: number;
  severity: ErrorSeverity;
  category: ErrorCategory;
  platform?: string;
  locationId?: number;
  hotelId?: number;
  message: string;
  stack?: string;
  context?: Record<string, any>;
  retryCount?: number;
  resolved?: boolean;
  resolvedAt?: number;
  httpStatus?: number;
  apiEndpoint?: string;
  requestId?: string;
  userAgent?: string;
  ipAddress?: string;
}

export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByPlatform: Record<string, number>;
  recentErrors: ErrorDetails[];
  errorRate: number; // errors per hour
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  retryableErrors: ErrorCategory[];
}

export class ErrorHandler extends EventEmitter {
  private errors: Map<string, ErrorDetails> = new Map();
  private maxStoredErrors = 1000;
  private errorRetentionHours = 24;
  private retryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    retryableErrors: [
      ErrorCategory.API_ERROR,
      ErrorCategory.NETWORK_ERROR,
      ErrorCategory.RATE_LIMIT_ERROR,
      ErrorCategory.GOOGLE_API_ERROR,
      ErrorCategory.GOOGLE_QUOTA_ERROR
    ]
  };

  constructor() {
    super();
    this.setupCleanupInterval();
  }

  /**
   * Log an error with context and severity
   */
  logError(
    error: Error | string,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: {
      platform?: string;
      locationId?: number;
      hotelId?: number;
      additionalData?: Record<string, any>;
    }
  ): string {
    const errorId = this.generateErrorId();
    const timestamp = Date.now();
    
    const errorDetails: ErrorDetails = {
      id: errorId,
      timestamp,
      severity,
      category,
      platform: context?.platform,
      locationId: context?.locationId,
      hotelId: context?.hotelId,
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      context: context?.additionalData,
      retryCount: 0,
      resolved: false,
    };

    this.errors.set(errorId, errorDetails);
    
    // Emit error event for real-time monitoring
    this.emit('error', errorDetails);
    
    // Log to console with appropriate level
    this.logToConsole(errorDetails);
    
    // Handle critical errors immediately
    if (severity === ErrorSeverity.CRITICAL) {
      this.handleCriticalError(errorDetails);
    }

    // Clean up old errors if we exceed the limit
    this.cleanupOldErrors();

    return errorId;
  }

  /**
   * Mark an error as resolved
   */
  resolveError(errorId: string): boolean {
    const error = this.errors.get(errorId);
    if (error) {
      error.resolved = true;
      error.resolvedAt = Date.now();
      this.emit('errorResolved', error);
      return true;
    }
    return false;
  }

  /**
   * Increment retry count for an error
   */
  incrementRetryCount(errorId: string): void {
    const error = this.errors.get(errorId);
    if (error) {
      error.retryCount = (error.retryCount || 0) + 1;
      this.emit('errorRetry', error);
    }
  }

  /**
   * Get error metrics and statistics
   */
  getMetrics(): ErrorMetrics {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const recentErrors = Array.from(this.errors.values())
      .filter(error => error.timestamp >= oneHourAgo);

    const errorsByCategory = {} as Record<ErrorCategory, number>;
    const errorsBySeverity = {} as Record<ErrorSeverity, number>;
    const errorsByPlatform = {} as Record<string, number>;

    // Initialize counters
    Object.values(ErrorCategory).forEach(category => {
      errorsByCategory[category] = 0;
    });
    Object.values(ErrorSeverity).forEach(severity => {
      errorsBySeverity[severity] = 0;
    });

    // Count errors
    Array.from(this.errors.values()).forEach(error => {
      errorsByCategory[error.category]++;
      errorsBySeverity[error.severity]++;
      
      if (error.platform) {
        errorsByPlatform[error.platform] = (errorsByPlatform[error.platform] || 0) + 1;
      }
    });

    return {
      totalErrors: this.errors.size,
      errorsByCategory,
      errorsBySeverity,
      errorsByPlatform,
      recentErrors: recentErrors.slice(-50), // Last 50 recent errors
      errorRate: recentErrors.length, // Errors in the last hour
    };
  }

  /**
   * Get errors by criteria
   */
  getErrors(criteria?: {
    category?: ErrorCategory;
    severity?: ErrorSeverity;
    platform?: string;
    hotelId?: number;
    resolved?: boolean;
    limit?: number;
  }): ErrorDetails[] {
    let errors = Array.from(this.errors.values());

    if (criteria) {
      if (criteria.category) {
        errors = errors.filter(error => error.category === criteria.category);
      }
      if (criteria.severity) {
        errors = errors.filter(error => error.severity === criteria.severity);
      }
      if (criteria.platform) {
        errors = errors.filter(error => error.platform === criteria.platform);
      }
      if (criteria.hotelId) {
        errors = errors.filter(error => error.hotelId === criteria.hotelId);
      }
      if (criteria.resolved !== undefined) {
        errors = errors.filter(error => error.resolved === criteria.resolved);
      }
    }

    // Sort by timestamp (newest first)
    errors.sort((a, b) => b.timestamp - a.timestamp);

    if (criteria?.limit) {
      errors = errors.slice(0, criteria.limit);
    }

    return errors;
  }

  /**
   * Clear all errors (for testing or maintenance)
   */
  clearErrors(): void {
    this.errors.clear();
    this.emit('errorsCleared');
  }

  /**
   * Generate a unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log error to console with appropriate formatting
   */
  private logToConsole(error: ErrorDetails): void {
    const timestamp = new Date(error.timestamp).toISOString();
    const prefix = `[${timestamp}] [${error.severity.toUpperCase()}] [${error.category}]`;
    
    if (error.platform) {
      console.log(`${prefix} [${error.platform}]`);
    }
    
    console.log(`${prefix} ${error.message}`);
    
    if (error.context) {
      console.log(`${prefix} Context:`, error.context);
    }
    
    if (error.stack && error.severity === ErrorSeverity.CRITICAL) {
      console.log(`${prefix} Stack:`, error.stack);
    }
  }

  /**
   * Handle critical errors with immediate attention
   */
  private handleCriticalError(error: ErrorDetails): void {
    console.error('🚨 CRITICAL ERROR DETECTED 🚨');
    console.error('Error ID:', error.id);
    console.error('Message:', error.message);
    console.error('Platform:', error.platform || 'Unknown');
    console.error('Context:', error.context);
    
    // In production, this would trigger alerts (email, Slack, etc.)
    this.emit('criticalError', error);
  }

  /**
   * Clean up old errors to prevent memory leaks
   */
  private cleanupOldErrors(): void {
    const now = Date.now();
    const retentionTime = this.errorRetentionHours * 60 * 60 * 1000;
    const cutoffTime = now - retentionTime;

    // Remove old errors
    for (const [id, error] of Array.from(this.errors.entries())) {
      if (error.timestamp < cutoffTime) {
        this.errors.delete(id);
      }
    }

    // If still too many errors, remove oldest ones
    if (this.errors.size > this.maxStoredErrors) {
      const sortedErrors = Array.from(this.errors.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp);
      
      const toRemove = this.errors.size - this.maxStoredErrors;
      for (let i = 0; i < toRemove; i++) {
        this.errors.delete(sortedErrors[i][0]);
      }
    }
  }

  /**
   * Execute a function with automatic retry logic
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    context: {
      operationName: string;
      platform?: string;
      hotelId?: number;
      customRetryConfig?: Partial<RetryConfig>;
    }
  ): Promise<T> {
    const config = { ...this.retryConfig, ...context.customRetryConfig };
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        const result = await operation();

        // If we had previous failures but this succeeded, log the recovery
        if (attempt > 0) {
          console.log(`✅ Operation '${context.operationName}' succeeded after ${attempt} retries`);
        }

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Determine if this error is retryable
        const errorCategory = this.categorizeError(lastError);
        const isRetryable = config.retryableErrors.includes(errorCategory);

        // If this is the last attempt or error is not retryable, don't retry
        if (attempt >= config.maxRetries || !isRetryable) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
          config.maxDelay
        );

        console.log(`⚠️ Operation '${context.operationName}' failed (attempt ${attempt + 1}/${config.maxRetries + 1}). Retrying in ${delay}ms...`);
        console.log(`Error: ${lastError.message}`);

        // Log the retry attempt
        this.logError(
          lastError,
          errorCategory,
          ErrorSeverity.MEDIUM,
          {
            platform: context.platform,
            hotelId: context.hotelId,
            additionalData: {
              operation: context.operationName,
              attempt: attempt + 1,
              willRetry: true,
              nextRetryIn: delay
            }
          }
        );

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // All retries failed, log final error and throw
    if (lastError) {
      const errorCategory = this.categorizeError(lastError);
      this.logError(
        lastError,
        errorCategory,
        ErrorSeverity.HIGH,
        {
          platform: context.platform,
          hotelId: context.hotelId,
          additionalData: {
            operation: context.operationName,
            totalAttempts: config.maxRetries + 1,
            finalFailure: true
          }
        }
      );

      throw new Error(`Operation '${context.operationName}' failed after ${config.maxRetries + 1} attempts: ${lastError.message}`);
    }

    throw new Error(`Operation '${context.operationName}' failed with unknown error`);
  }

  /**
   * Categorize an error based on its properties
   */
  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();

    // Google-specific error categorization
    if (message.includes('google') || message.includes('googleapis')) {
      if (message.includes('quota') || message.includes('rate limit') || message.includes('too many requests')) {
        return ErrorCategory.GOOGLE_QUOTA_ERROR;
      }
      if (message.includes('unauthorized') || message.includes('forbidden') || message.includes('authentication') || message.includes('credentials')) {
        return ErrorCategory.GOOGLE_AUTH_ERROR;
      }
      return ErrorCategory.GOOGLE_API_ERROR;
    }

    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return ErrorCategory.NETWORK_ERROR;
    }

    if (message.includes('rate limit') || message.includes('quota') || message.includes('too many requests')) {
      return ErrorCategory.RATE_LIMIT_ERROR;
    }

    if (message.includes('unauthorized') || message.includes('forbidden') || message.includes('authentication')) {
      return ErrorCategory.AUTHENTICATION_ERROR;
    }

    if (message.includes('validation') || message.includes('invalid') || message.includes('bad request')) {
      return ErrorCategory.VALIDATION_ERROR;
    }

    if (message.includes('database') || message.includes('sql') || message.includes('constraint')) {
      return ErrorCategory.DATABASE_ERROR;
    }

    if (message.includes('sync') || message.includes('synchronization')) {
      return ErrorCategory.SYNC_ERROR;
    }

    // Check if it's an API error based on error properties
    if ('response' in error || 'status' in error || message.includes('api')) {
      return ErrorCategory.API_ERROR;
    }

    return ErrorCategory.UNKNOWN_ERROR;
  }

  /**
   * Specialized error handler for Google Business API errors
   */
  logGoogleBusinessError(
    error: any,
    operation: string,
    context?: {
      hotelId?: number;
      locationId?: number;
      reviewId?: string;
      additionalData?: Record<string, any>;
    }
  ): string {
    let category = ErrorCategory.GOOGLE_API_ERROR;
    let severity = ErrorSeverity.MEDIUM;
    let httpStatus: number | undefined;
    let apiEndpoint: string | undefined;

    // Extract Google API specific information
    if (error.response) {
      httpStatus = error.response.status;
      apiEndpoint = error.config?.url;

      // Categorize based on HTTP status
      switch (httpStatus) {
        case 401:
        case 403:
          category = ErrorCategory.GOOGLE_AUTH_ERROR;
          severity = ErrorSeverity.HIGH;
          break;
        case 429:
          category = ErrorCategory.GOOGLE_QUOTA_ERROR;
          severity = ErrorSeverity.MEDIUM;
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          category = ErrorCategory.GOOGLE_API_ERROR;
          severity = ErrorSeverity.HIGH;
          break;
        default:
          category = ErrorCategory.GOOGLE_API_ERROR;
          severity = ErrorSeverity.MEDIUM;
      }
    }

    return this.logError(
      error instanceof Error ? error : new Error(String(error)),
      category,
      severity,
      {
        platform: 'google',
        hotelId: context?.hotelId,
        locationId: context?.locationId,
        additionalData: {
          operation,
          reviewId: context?.reviewId,
          httpStatus,
          apiEndpoint,
          ...context?.additionalData
        }
      }
    );
  }

  /**
   * Enhanced logging with structured data
   */
  logStructured(level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: Record<string, any>): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...data
    };

    // In production, this would go to a proper logging service
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, data ? JSON.stringify(data, null, 2) : '');

    // Emit log event for external log processors
    this.emit('log', logEntry);
  }

  /**
   * Set up periodic cleanup of old errors
   */
  private setupCleanupInterval(): void {
    // Clean up every hour
    setInterval(() => {
      this.cleanupOldErrors();
    }, 60 * 60 * 1000);
  }
}

/**
 * Monitoring service for tracking system health and performance
 */
export class MonitoringService extends EventEmitter {
  private metrics: Map<string, any> = new Map();
  private healthChecks: Map<string, () => Promise<boolean>> = new Map();

  constructor() {
    super();
    this.setupDefaultHealthChecks();
    this.startHealthCheckInterval();
  }

  /**
   * Record a metric value
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const timestamp = Date.now();
    const metric = {
      name,
      value,
      timestamp,
      tags: tags || {},
    };

    const key = `${name}_${timestamp}`;
    this.metrics.set(key, metric);

    this.emit('metric', metric);
  }



  /**
   * Register a health check
   */
  registerHealthCheck(name: string, checkFunction: () => Promise<boolean>): void {
    this.healthChecks.set(name, checkFunction);
  }

  /**
   * Run all health checks
   */
  async runHealthChecks(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [name, checkFunction] of Array.from(this.healthChecks.entries())) {
      try {
        results[name] = await checkFunction();
      } catch (error) {
        results[name] = false;
        errorHandler.logError(
          error instanceof Error ? error : new Error(String(error)),
          ErrorCategory.UNKNOWN_ERROR,
          ErrorSeverity.MEDIUM,
          { additionalData: { healthCheck: name } }
        );
      }
    }

    return results;
  }

  /**
   * Get system health status
   */
  async getSystemHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, boolean>;
    errors: ErrorMetrics;
    uptime: number;
  }> {
    const checks = await this.runHealthChecks();
    const errors = errorHandler.getMetrics();
    const uptime = process.uptime();

    const failedChecks = Object.values(checks).filter(result => !result).length;
    const totalChecks = Object.keys(checks).length;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (failedChecks === 0) {
      status = 'healthy';
    } else if (failedChecks < totalChecks / 2) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      checks,
      errors,
      uptime,
    };
  }

  private setupDefaultHealthChecks(): void {
    // Database health check
    this.registerHealthCheck('database', async () => {
      try {
        // Simple database connectivity check
        return true; // Placeholder - would check actual database connection
      } catch {
        return false;
      }
    });

    // Memory usage health check
    this.registerHealthCheck('memory', async () => {
      const memUsage = process.memoryUsage();
      const maxMemory = 1024 * 1024 * 1024; // 1GB limit
      return memUsage.heapUsed < maxMemory;
    });

    // Error rate health check
    this.registerHealthCheck('errorRate', async () => {
      const metrics = errorHandler.getMetrics();
      return metrics.errorRate < 10; // Less than 10 errors per hour
    });
  }

  private startHealthCheckInterval(): void {
    // Run health checks every 5 minutes
    setInterval(async () => {
      const health = await this.getSystemHealth();
      this.emit('healthCheck', health);

      if (health.status === 'unhealthy') {
        errorHandler.logError(
          'System health check failed',
          ErrorCategory.UNKNOWN_ERROR,
          ErrorSeverity.HIGH,
          { additionalData: { healthStatus: health } }
        );
      }
    }, 5 * 60 * 1000);
  }
}

// Export singleton instances
export const errorHandler = new ErrorHandler();
export const monitoringService = new MonitoringService();
